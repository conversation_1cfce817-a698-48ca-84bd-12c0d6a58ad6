-- Migration: 008_views_and_functions.sql
-- Description: إنشاء Views للتقارير والدوال المساعدة المتقدمة
-- Created: 2024-01-15
-- Author: School Bus Management System

SET search_path TO school_bus_system, public;

-- عرض إحصائيات الحضور
CREATE VIEW student_attendance_summary AS
SELECT 
    s.tenant_id,
    s.id as student_id,
    s.name_ar as student_name,
    s.grade,
    s.class,
    COUNT(a.id) as total_records,
    COUNT(CASE WHEN a.status = 'boarded' THEN 1 END) as present_days,
    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_days,
    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
    ROUND(
        COUNT(CASE WHEN a.status = 'boarded' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN a.status IN ('boarded', 'absent') THEN 1 END), 0), 
        2
    ) as attendance_rate,
    MAX(a.date) as last_attendance_date
FROM students s
LEFT JOIN attendance a ON s.id = a.student_id
WHERE s.is_active = true
GROUP BY s.tenant_id, s.id, s.name_ar, s.grade, s.class;

-- عرض إحصائيات الحافلات
CREATE VIEW bus_utilization_summary AS
SELECT 
    b.tenant_id,
    b.id as bus_id,
    b.bus_number,
    b.plate_number,
    b.capacity,
    d.name_ar as driver_name,
    COUNT(DISTINCT r.id) as assigned_routes,
    COUNT(DISTINCT s.id) as total_students,
    ROUND(
        COUNT(DISTINCT s.id) * 100.0 / NULLIF(b.capacity, 0), 
        2
    ) as utilization_rate,
    b.status,
    b.last_maintenance_date,
    b.next_maintenance_date
FROM buses b
LEFT JOIN drivers d ON b.driver_id = d.id
LEFT JOIN routes r ON b.id = r.bus_id AND r.is_active = true
LEFT JOIN attendance a ON b.id = a.bus_id 
    AND a.date >= CURRENT_DATE - INTERVAL '30 days'
LEFT JOIN students s ON a.student_id = s.id AND s.is_active = true
WHERE b.is_active = true
GROUP BY b.tenant_id, b.id, b.bus_number, b.plate_number, b.capacity, 
         d.name_ar, b.status, b.last_maintenance_date, b.next_maintenance_date;

-- عرض إحصائيات المسارات
CREATE VIEW route_performance_summary AS
SELECT 
    r.tenant_id,
    r.id as route_id,
    r.name as route_name,
    r.start_time,
    r.end_time,
    r.estimated_duration,
    r.distance_km,
    b.bus_number,
    d.name_ar as driver_name,
    COUNT(DISTINCT st.id) as total_stops,
    COUNT(DISTINCT s.id) as registered_students,
    COUNT(DISTINCT CASE WHEN a.status = 'boarded' AND a.date >= CURRENT_DATE - INTERVAL '7 days' THEN a.student_id END) as active_students_week,
    ROUND(
        AVG(CASE WHEN a.status = 'boarded' THEN 1.0 ELSE 0.0 END) * 100, 
        2
    ) as average_attendance_rate
FROM routes r
LEFT JOIN buses b ON r.bus_id = b.id
LEFT JOIN drivers d ON r.driver_id = d.id
LEFT JOIN stops st ON r.id = st.route_id
LEFT JOIN attendance a ON r.id = a.route_id 
    AND a.date >= CURRENT_DATE - INTERVAL '30 days'
LEFT JOIN students s ON a.student_id = s.id AND s.is_active = true
WHERE r.is_active = true
GROUP BY r.tenant_id, r.id, r.name, r.start_time, r.end_time, 
         r.estimated_duration, r.distance_km, b.bus_number, d.name_ar;

-- عرض إحصائيات يومية للحضور
CREATE VIEW daily_attendance_stats AS
SELECT 
    a.tenant_id,
    a.date,
    COUNT(DISTINCT a.student_id) as total_students,
    COUNT(CASE WHEN a.status = 'boarded' THEN 1 END) as present_count,
    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
    ROUND(
        COUNT(CASE WHEN a.status = 'boarded' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN a.status IN ('boarded', 'absent') THEN 1 END), 0), 
        2
    ) as attendance_percentage,
    COUNT(DISTINCT a.bus_id) as active_buses,
    COUNT(DISTINCT a.route_id) as active_routes
FROM attendance a
WHERE a.date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY a.tenant_id, a.date
ORDER BY a.date DESC;

-- عرض إحصائيات المستأجرين
CREATE VIEW tenant_overview AS
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    t.domain,
    t.is_active,
    t.created_at,
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT s.id) as total_students,
    COUNT(DISTINCT d.id) as total_drivers,
    COUNT(DISTINCT b.id) as total_buses,
    COUNT(DISTINCT r.id) as total_routes,
    COUNT(DISTINCT CASE WHEN u.last_login_at >= CURRENT_DATE - INTERVAL '7 days' THEN u.id END) as active_users_week,
    COUNT(DISTINCT CASE WHEN a.date >= CURRENT_DATE - INTERVAL '7 days' THEN a.student_id END) as active_students_week
FROM tenants t
LEFT JOIN users u ON t.id = u.tenant_id AND u.is_active = true
LEFT JOIN students s ON t.id = s.tenant_id AND s.is_active = true
LEFT JOIN drivers d ON t.id = d.tenant_id AND d.is_active = true
LEFT JOIN buses b ON t.id = b.tenant_id AND b.is_active = true
LEFT JOIN routes r ON t.id = r.tenant_id AND r.is_active = true
LEFT JOIN attendance a ON t.id = a.tenant_id
GROUP BY t.id, t.name, t.domain, t.is_active, t.created_at;

-- دالة للتحقق من صحة قاعدة البيانات
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    details TEXT,
    count_value BIGINT
) AS $$
BEGIN
    -- فحص الجداول الأساسية
    RETURN QUERY
    SELECT 
        'Tables Count'::TEXT,
        'OK'::TEXT,
        'All core tables exist'::TEXT,
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = 'school_bus_system')::BIGINT;
    
    -- فحص المستأجرين
    RETURN QUERY
    SELECT 
        'Tenants'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::TEXT,
        ('Total tenants: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM tenants;
    
    -- فحص الأدوار
    RETURN QUERY
    SELECT 
        'Roles'::TEXT,
        CASE WHEN COUNT(*) >= 6 THEN 'OK' ELSE 'WARNING' END::TEXT,
        ('Total roles: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM roles;
    
    -- فحص الصلاحيات
    RETURN QUERY
    SELECT 
        'Permissions'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::TEXT,
        ('Total permissions: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM permissions;
    
    -- فحص المستخدمين
    RETURN QUERY
    SELECT 
        'Users'::TEXT,
        'OK'::TEXT,
        ('Total users: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM users;
    
    -- فحص الطلاب
    RETURN QUERY
    SELECT 
        'Students'::TEXT,
        'OK'::TEXT,
        ('Total students: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM students;
    
    -- فحص السائقين
    RETURN QUERY
    SELECT 
        'Drivers'::TEXT,
        'OK'::TEXT,
        ('Total drivers: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM drivers;
    
    -- فحص الحافلات
    RETURN QUERY
    SELECT 
        'Buses'::TEXT,
        'OK'::TEXT,
        ('Total buses: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM buses;
    
    -- فحص المسارات
    RETURN QUERY
    SELECT 
        'Routes'::TEXT,
        'OK'::TEXT,
        ('Total routes: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM routes;
    
    -- فحص نقاط التوقف
    RETURN QUERY
    SELECT 
        'Stops'::TEXT,
        'OK'::TEXT,
        ('Total stops: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM stops;
    
    -- فحص RLS
    RETURN QUERY
    SELECT 
        'RLS Policies'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END::TEXT,
        ('Active RLS policies: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM pg_policies 
    WHERE schemaname = 'school_bus_system';
    
    -- فحص الفهارس
    RETURN QUERY
    SELECT 
        'Indexes'::TEXT,
        'OK'::TEXT,
        ('Total indexes: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM pg_indexes 
    WHERE schemaname = 'school_bus_system';
    
    -- فحص PostGIS
    RETURN QUERY
    SELECT 
        'PostGIS Extension'::TEXT,
        CASE WHEN EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'postgis') 
             THEN 'OK' ELSE 'ERROR' END::TEXT,
        'PostGIS spatial extension'::TEXT,
        CASE WHEN EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'postgis') 
             THEN 1 ELSE 0 END::BIGINT;
             
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات المستأجر
CREATE OR REPLACE FUNCTION get_tenant_statistics(tenant_uuid UUID)
RETURNS TABLE(
    metric_name TEXT,
    metric_value BIGINT,
    metric_description TEXT
) AS $$
BEGIN
    -- تعيين سياق المستأجر
    PERFORM set_tenant_context(tenant_uuid);
    
    -- إحصائيات المستخدمين
    RETURN QUERY
    SELECT 
        'total_users'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي المستخدمين'::TEXT
    FROM users WHERE tenant_id = tenant_uuid AND is_active = true;
    
    -- إحصائيات الطلاب
    RETURN QUERY
    SELECT 
        'total_students'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي الطلاب'::TEXT
    FROM students WHERE tenant_id = tenant_uuid AND is_active = true;
    
    -- إحصائيات الحافلات
    RETURN QUERY
    SELECT 
        'total_buses'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي الحافلات'::TEXT
    FROM buses WHERE tenant_id = tenant_uuid AND is_active = true;
    
    -- إحصائيات المسارات
    RETURN QUERY
    SELECT 
        'total_routes'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي المسارات'::TEXT
    FROM routes WHERE tenant_id = tenant_uuid AND is_active = true;
    
    -- حضور اليوم
    RETURN QUERY
    SELECT 
        'today_attendance'::TEXT,
        COUNT(*)::BIGINT,
        'حضور اليوم'::TEXT
    FROM attendance 
    WHERE tenant_id = tenant_uuid 
    AND date = CURRENT_DATE 
    AND status = 'boarded';
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إضافة التعليقات على Views والدوال
COMMENT ON VIEW student_attendance_summary IS 'ملخص حضور الطلاب مع معدلات الحضور';
COMMENT ON VIEW bus_utilization_summary IS 'إحصائيات استخدام الحافلات ومعدلات الاستغلال';
COMMENT ON VIEW route_performance_summary IS 'أداء المسارات وإحصائيات الطلاب النشطين';
COMMENT ON VIEW daily_attendance_stats IS 'إحصائيات الحضور اليومية';
COMMENT ON VIEW tenant_overview IS 'نظرة عامة شاملة على المستأجرين';
COMMENT ON FUNCTION database_health_check() IS 'فحص شامل لصحة قاعدة البيانات';
COMMENT ON FUNCTION get_tenant_statistics(UUID) IS 'إحصائيات مفصلة لمستأجر محدد';
