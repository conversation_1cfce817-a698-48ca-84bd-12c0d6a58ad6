-- Migration: 006_seed_data.sql
SET search_path TO school_bus_system, public;

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, name_ar, name_en, description, role_scope, is_system_role) VALUES
('system_admin', 'مدير النظام', 'System Admin', 'مدير النظام العام', 'global', true),
('school_admin', 'مدير المدرسة', 'School Admin', 'مدير المدرسة', 'tenant', false),
('driver', 'سائق', 'Driver', 'سائق الحافلة', 'assigned', false),
('parent', 'ولي أمر', 'Parent', 'ولي أمر الطالب', 'children', false),
('student', 'طالب', 'Student', 'طالب', 'personal', false),
('traffic_supervisor', 'مشرف حركة', 'Traffic Supervisor', 'مشرف حركة المرور', 'tenant', false)
ON CONFLICT (name) DO NOTHING;

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, name_ar, name_en, description, module, action, resource) VALUES
-- صلاحيات إدارة النظام
('SYSTEM_MANAGE_ALL', 'إدارة النظام الكاملة', 'Full System Management', 'إدارة كاملة للنظام', 'system', 'manage', 'all'),
('TENANTS_MANAGE', 'إدارة المدارس', 'Manage Schools', 'إدارة المدارس والمستأجرين', 'tenants', 'manage', 'tenants'),

-- صلاحيات إدارة المستخدمين
('USERS_VIEW', 'عرض المستخدمين', 'View Users', 'عرض قائمة المستخدمين', 'users', 'view', 'users'),
('USERS_CREATE', 'إنشاء مستخدمين', 'Create Users', 'إنشاء مستخدمين جدد', 'users', 'create', 'users'),
('USERS_UPDATE', 'تعديل المستخدمين', 'Update Users', 'تعديل بيانات المستخدمين', 'users', 'update', 'users'),
('USERS_DELETE', 'حذف المستخدمين', 'Delete Users', 'حذف المستخدمين', 'users', 'delete', 'users'),

-- صلاحيات إدارة الطلاب
('STUDENTS_VIEW', 'عرض الطلاب', 'View Students', 'عرض قائمة الطلاب', 'students', 'view', 'students'),
('STUDENTS_CREATE', 'إنشاء طلاب', 'Create Students', 'إنشاء طلاب جدد', 'students', 'create', 'students'),
('STUDENTS_UPDATE', 'تعديل الطلاب', 'Update Students', 'تعديل بيانات الطلاب', 'students', 'update', 'students'),
('STUDENTS_DELETE', 'حذف الطلاب', 'Delete Students', 'حذف الطلاب', 'students', 'delete', 'students'),

-- صلاحيات إدارة الحافلات
('BUSES_VIEW', 'عرض الحافلات', 'View Buses', 'عرض قائمة الحافلات', 'buses', 'view', 'buses'),
('BUSES_CREATE', 'إنشاء حافلات', 'Create Buses', 'إنشاء حافلات جديدة', 'buses', 'create', 'buses'),
('BUSES_UPDATE', 'تعديل الحافلات', 'Update Buses', 'تعديل بيانات الحافلات', 'buses', 'update', 'buses'),
('BUSES_DELETE', 'حذف الحافلات', 'Delete Buses', 'حذف الحافلات', 'buses', 'delete', 'buses'),

-- صلاحيات إدارة المسارات
('ROUTES_VIEW', 'عرض المسارات', 'View Routes', 'عرض قائمة المسارات', 'routes', 'view', 'routes'),
('ROUTES_CREATE', 'إنشاء مسارات', 'Create Routes', 'إنشاء مسارات جديدة', 'routes', 'create', 'routes'),
('ROUTES_UPDATE', 'تعديل المسارات', 'Update Routes', 'تعديل بيانات المسارات', 'routes', 'update', 'routes'),
('ROUTES_DELETE', 'حذف المسارات', 'Delete Routes', 'حذف المسارات', 'routes', 'delete', 'routes'),

-- صلاحيات الحضور
('ATTENDANCE_VIEW', 'عرض الحضور', 'View Attendance', 'عرض سجلات الحضور', 'attendance', 'view', 'attendance'),
('ATTENDANCE_RECORD', 'تسجيل الحضور', 'Record Attendance', 'تسجيل حضور الطلاب', 'attendance', 'create', 'attendance'),

-- صلاحيات التقارير
('REPORTS_VIEW', 'عرض التقارير', 'View Reports', 'عرض التقارير والإحصائيات', 'reports', 'view', 'reports'),
('REPORTS_EXPORT', 'تصدير التقارير', 'Export Reports', 'تصدير التقارير', 'reports', 'export', 'reports')
ON CONFLICT (name) DO NOTHING;

SELECT 'Migration 006 completed successfully' as result;
