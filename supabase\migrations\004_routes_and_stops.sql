-- Migration: 004_routes_and_stops.sql
-- Description: إن<PERSON><PERSON><PERSON> جداول المسارات ونقاط التوقف
-- Created: 2024-01-15
-- Author: School Bus Management System

SET search_path TO school_bus_system, public;

-- إن<PERSON><PERSON><PERSON> جدول المسارات
CREATE TABLE IF NOT EXISTS routes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- معلومات المسار
    name VARCHAR(255) NOT NULL,
    description TEXT,
    route_type VARCHAR(20) DEFAULT 'regular' CHECK (route_type IN ('regular', 'express', 'special')),
    
    -- الحافلة والسائق المعينين
    bus_id UUID REFERENCES buses(id),
    driver_id UUID REFERENCES drivers(id),
    
    -- التوقيت
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    
    -- أيام العمل (1=الأحد، 7=السبت)
    working_days INTEGER[] DEFAULT ARRAY[1,2,3,4,5],
    
    -- معلومات إضافية
    estimated_duration INTEGER, -- بالدقائق
    distance_km DECIMAL(8,2),
    max_students INTEGER, -- الحد الأقصى للطلاب
    
    -- معلومات المسار
    route_path GEOGRAPHY(LineString, 4326), -- مسار الحافلة
    
    -- الانتماء
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- حالة النشاط
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول نقاط التوقف
CREATE TABLE IF NOT EXISTS stops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- المسار المرتبط
    route_id UUID NOT NULL REFERENCES routes(id) ON DELETE CASCADE,
    
    -- ترتيب النقطة في المسار
    stop_order INTEGER NOT NULL,
    
    -- الموقع (PostGIS)
    location GEOGRAPHY(Point, 4326) NOT NULL,
    
    -- الأسماء
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    
    -- معلومات إضافية
    description TEXT,
    landmarks TEXT, -- معالم مميزة
    
    -- التوقيت التقديري
    arrival_time_estimate TIME,
    departure_time_estimate TIME,
    
    -- نوع النقطة
    stop_type VARCHAR(20) DEFAULT 'pickup' CHECK (stop_type IN ('pickup', 'dropoff', 'both')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيد فريد لضمان عدم تكرار الترتيب في نفس المسار
    UNIQUE(route_id, stop_order)
);

-- إنشاء جدول الحضور والانصراف
CREATE TABLE IF NOT EXISTS attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- الطالب والحافلة
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    bus_id UUID NOT NULL REFERENCES buses(id),
    route_id UUID REFERENCES routes(id),
    stop_id UUID REFERENCES stops(id),
    
    -- التاريخ والوقت
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- حالة الحضور
    status VARCHAR(20) NOT NULL CHECK (status IN ('boarded', 'alighted', 'absent', 'late', 'early')),
    
    -- الموقع عند التسجيل
    location GEOGRAPHY(Point, 4326),
    
    -- معلومات إضافية
    notes TEXT,
    recorded_by UUID REFERENCES users(id), -- من سجل الحضور
    method VARCHAR(20) DEFAULT 'manual' CHECK (method IN ('manual', 'qr_code', 'nfc', 'biometric')),
    
    -- الانتماء
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- نوع الإشعار
    type VARCHAR(50) NOT NULL,
    category VARCHAR(30) DEFAULT 'general' CHECK (category IN ('general', 'attendance', 'emergency', 'maintenance', 'payment')),
    
    -- المحتوى
    title_ar VARCHAR(255) NOT NULL,
    title_en VARCHAR(255),
    message_ar TEXT NOT NULL,
    message_en TEXT,
    
    -- المستلم
    recipient_role VARCHAR(50), -- إرسال لدور معين
    recipient_id UUID REFERENCES users(id), -- أو مستخدم محدد
    
    -- حالة القراءة
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- بيانات إضافية
    metadata JSONB DEFAULT '{}',
    action_url TEXT, -- رابط لإجراء معين
    
    -- الأولوية
    priority VARCHAR(20) DEFAULT 'medium' 
        CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- طريقة الإرسال
    delivery_methods TEXT[] DEFAULT ARRAY['app'], -- app, email, sms, push
    
    -- حالة الإرسال
    sent_at TIMESTAMP WITH TIME ZONE,
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    
    -- الانتماء
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس للمسارات
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_routes_bus_id ON routes(bus_id);
CREATE INDEX IF NOT EXISTS idx_routes_driver_id ON routes(driver_id);
CREATE INDEX IF NOT EXISTS idx_routes_active ON routes(tenant_id, is_active) WHERE is_active = true;

-- فهارس لنقاط التوقف
CREATE INDEX IF NOT EXISTS idx_stops_route_id ON stops(route_id);
CREATE INDEX IF NOT EXISTS idx_stops_order ON stops(route_id, stop_order);

-- فهارس مكانية للمواقع
CREATE INDEX IF NOT EXISTS idx_routes_path ON routes USING GIST(route_path);
CREATE INDEX IF NOT EXISTS idx_stops_location ON stops USING GIST(location);

-- فهارس للحضور
CREATE INDEX IF NOT EXISTS idx_attendance_student_date ON attendance(student_id, date);
CREATE INDEX IF NOT EXISTS idx_attendance_bus_date ON attendance(bus_id, date);
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_date ON attendance(tenant_id, date);
CREATE INDEX IF NOT EXISTS idx_attendance_timestamp ON attendance(timestamp);
CREATE INDEX IF NOT EXISTS idx_attendance_status ON attendance(status, tenant_id);

-- فهارس للإشعارات
CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON notifications(recipient_id, is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_role ON notifications(recipient_role, tenant_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type, tenant_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority, tenant_id);

-- إنشاء triggers لتحديث updated_at
CREATE TRIGGER update_routes_updated_at 
    BEFORE UPDATE ON routes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stops_updated_at 
    BEFORE UPDATE ON stops
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إضافة التعليقات
COMMENT ON TABLE routes IS 'جدول المسارات';
COMMENT ON TABLE stops IS 'جدول نقاط التوقف';
COMMENT ON TABLE attendance IS 'جدول الحضور والانصراف';
COMMENT ON TABLE notifications IS 'جدول الإشعارات';

COMMENT ON COLUMN routes.route_path IS 'مسار الحافلة الجغرافي (PostGIS LineString)';
COMMENT ON COLUMN stops.location IS 'موقع نقطة التوقف (PostGIS Point)';
COMMENT ON COLUMN attendance.location IS 'موقع تسجيل الحضور (PostGIS Point)';
COMMENT ON COLUMN notifications.metadata IS 'بيانات إضافية للإشعار (JSON)';
