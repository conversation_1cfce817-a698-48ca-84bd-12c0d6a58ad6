#!/usr/bin/env node

/**
 * سكربت الترحيل التلقائي إلى Supabase
 * Auto Migration Script to Supabase
 * 
 * الاستخدام:
 * node scripts/auto-migrate.js --token=your_access_token
 * 
 * أو تعيين متغير البيئة:
 * SUPABASE_ACCESS_TOKEN=your_token node scripts/auto-migrate.js
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// إعدادات المشروع
const PROJECT_ID = 'lfvnkfzlztjnwyluzoii';
const API_BASE = 'https://api.supabase.com/v1';
const DB_API = `${API_BASE}/projects/${PROJECT_ID}/database/query`;

// ألوان للمخرجات
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// دالة طباعة ملونة
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}[STEP]${colors.reset} ${msg}`)
};

// الحصول على Access Token
function getAccessToken() {
  // من معاملات سطر الأوامر
  const tokenArg = process.argv.find(arg => arg.startsWith('--token='));
  if (tokenArg) {
    return tokenArg.split('=')[1];
  }
  
  // من متغيرات البيئة
  return process.env.SUPABASE_ACCESS_TOKEN;
}

// قراءة ملف SQL
function readSQLFile(filename) {
  const filePath = path.join(__dirname, '..', 'supabase', 'migrations', filename);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`ملف الترحيل غير موجود: ${filename}`);
  }
  
  return fs.readFileSync(filePath, 'utf8');
}

// تنفيذ استعلام SQL
function executeSQL(query, description) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({ query });
    
    const options = {
      hostname: 'api.supabase.com',
      port: 443,
      path: `/v1/projects/${PROJECT_ID}/database/query`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAccessToken()}`,
        'Content-Length': Buffer.byteLength(data)
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          
          if (res.statusCode === 200) {
            log.success(`${description} - تم بنجاح`);
            resolve(result);
          } else {
            log.error(`${description} - فشل: ${result.message || responseData}`);
            reject(new Error(result.message || responseData));
          }
        } catch (error) {
          log.error(`خطأ في تحليل الاستجابة: ${error.message}`);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      log.error(`خطأ في الطلب: ${error.message}`);
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// فحص حالة المشروع
async function checkProjectStatus() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.supabase.com',
      port: 443,
      path: `/v1/projects/${PROJECT_ID}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const project = JSON.parse(data);
          
          if (res.statusCode === 200) {
            log.success(`المشروع نشط: ${project.name} (${project.status})`);
            resolve(project);
          } else {
            reject(new Error(`فشل في الوصول للمشروع: ${data}`));
          }
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
}

// قائمة ملفات الترحيل بالترتيب
const migrationFiles = [
  { file: '001_initial_setup.sql', description: 'الإعداد الأساسي والامتدادات' },
  { file: '002_core_tables.sql', description: 'الجداول الأساسية' },
  { file: '003_entity_tables.sql', description: 'جداول الكيانات' },
  { file: '004_routes_and_stops.sql', description: 'المسارات ونقاط التوقف' },
  { file: '005_rls_policies.sql', description: 'سياسات الأمان' },
  { file: '006_seed_data.sql', description: 'البيانات الأساسية' },
  { file: '007_sample_data.sql', description: 'البيانات التجريبية' },
  { file: '008_views_and_functions.sql', description: 'Views والدوال المتقدمة' }
];

// الدالة الرئيسية
async function main() {
  try {
    console.log(`${colors.magenta}
╔══════════════════════════════════════════════════════════════╗
║                    🚀 ترحيل قاعدة البيانات                    ║
║                  School Bus Database Migration               ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

    // فحص Access Token
    const token = getAccessToken();
    if (!token) {
      log.error('Access Token مطلوب!');
      log.info('استخدم: --token=your_token أو تعيين SUPABASE_ACCESS_TOKEN');
      process.exit(1);
    }

    log.info('بدء عملية الترحيل...');

    // فحص حالة المشروع
    log.step('فحص حالة المشروع...');
    await checkProjectStatus();

    // تنفيذ ملفات الترحيل
    let completedMigrations = 0;
    
    for (const migration of migrationFiles) {
      try {
        log.step(`تنفيذ: ${migration.description}`);
        
        const sqlContent = readSQLFile(migration.file);
        await executeSQL(sqlContent, migration.description);
        
        completedMigrations++;
        
        // تأخير قصير بين الترحيلات
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        log.warning(`تخطي ${migration.file}: ${error.message}`);
        // متابعة مع الملفات التالية
      }
    }

    // فحص صحة النظام
    log.step('فحص صحة النظام...');
    try {
      await executeSQL(
        "SELECT * FROM school_bus_system.database_health_check();",
        "فحص صحة قاعدة البيانات"
      );
    } catch (error) {
      log.warning('دالة فحص الصحة غير متاحة بعد');
    }

    // النتائج النهائية
    console.log(`${colors.green}
╔══════════════════════════════════════════════════════════════╗
║                        ✅ تم الانتهاء!                        ║
║                     Migration Complete!                     ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

    log.success(`تم تنفيذ ${completedMigrations}/${migrationFiles.length} ملف ترحيل`);
    log.info(`معرف المشروع: ${PROJECT_ID}`);
    log.info(`رابط المشروع: https://supabase.com/dashboard/project/${PROJECT_ID}`);
    
    console.log(`${colors.cyan}
الخطوات التالية:
1. احصل على مفاتيح API من لوحة التحكم
2. أعد إعداد متغيرات البيئة
3. ابدأ التطوير!
${colors.reset}`);

  } catch (error) {
    log.error(`فشل في الترحيل: ${error.message}`);
    process.exit(1);
  }
}

// تشغيل السكربت
if (require.main === module) {
  main();
}

module.exports = { executeSQL, checkProjectStatus };
