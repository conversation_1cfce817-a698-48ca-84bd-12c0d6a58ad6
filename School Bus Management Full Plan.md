**مقدمة**
فيما يلي تقسيم متكامل لمراحل تنفيذ مشروع نظام إدارة الحافلات المدرسية كنظام SaaS متعدد المستأجرين (Multi-Tenant)، من البداية وحتى الإطلاق والإنتاج. تم تقسيم العمل إلى مراحل مرتبة زمنيًا، بحيث يتركّز كل مرحلى على مجموعة محددة من الوظائف والمهام، مع ضمان توثيق كل مرحلة كما هو مطلوب (إنشاء ملفات `docs/changelog/[module].md` و`docs/details/[module].md` وتحديث ملف `docs/changelog.md` العام). لقد تم التنسيق وفق نظام الألوان والخطوط والهيكل التقني المحدد، مع مراعاة عدم التكرار والانسيابية بين المراحل.

**حالة التنفيذ: 🚀 بدء التنفيذ - المرحلة 1**

---

## المرحلة 1: إعداد البنية الأساسية للمشروع (Initial Setup & Architecture)

### الهدف الوظيفي

* تهيئة بنية المجلدات والملفات الرئيسية للمشروع وفق معمارية نظيفة (Clean Architecture + Modularization).
* ضبط إعدادات الـ Multi-Tenant العاملة على Supabase (PostgreSQL + PostGIS + RLS).

### المهام

1. إنشاء المستودع الرئيسي (`school-system/`) وضبط ملفات الضبط:

   * `README.md` يحتوي على ملخص المشروع ورابط المستودع.
   * `.env` و`.env.example` مع تعريف المتغيرات الأساسية (Supabase URL/KEY، Mapbox Token، Paymob Credentials، إلخ).
   * `package.json` أو `pubspec.yaml` بحسب الحاجة (React + TypeScript أو Flutter).
   * ملفات التهيئة: `tsconfig.json`، `vite.config.js` (إن كان React).
2. إنشاء الهيكل الأساسي للمجلدات:

   ```
   school-system/
   │
   ├── apps/                  
   │   └── README.md  # توضيح لمحتوى مجلد الوحدات (Plug-ins)
   ├── dashboards/           
   │   └── README.md  # وصف لوحات التحكم حسب الدور
   ├── ui/                    
   │   ├── components/         
   │   ├── layout/             
   │   ├── themes/             
   │   ├── utils/              
   │   └── index.ts            
   ├── scripts/               
   │   └── README.md  # شرح سكربتات التحضير والنشر
   ├── test/                  
   │   └── README.md  # وصف هيكلة الاختبارات
   ├── tmp/                   
   │   └── README.md  # ملفات تجريبية مؤقتة
   ├── docs/                  
   │   ├── changelog/        
   │   │   └── README.md  # تشيكل ملفات التغيير لكل وحدة
   │   ├── details/          
   │   │   └── README.md  # تفاصيل كل مرحلة
   │   ├── architecture.md 
   │   ├── setup-guide.md  
   │   ├── modules-list.md 
   │   └── database-schema.md
   ├── .env                   
   ├── README.md              
   └── tsconfig.json / vite.config.js  
   ```
3. إعداد قاعدة البيانات على Supabase:

   * إنشاء المشروع في Supabase.
   * تفعيل امتداد PostGIS.
   * تشغيل سياسة RLS (Row Level Security) على الجداول (tenant\_id موجود في كل جدول).
   * إعداد الجداول الأساسية (tenants، users، roles، permissions، role\_permission\_map).
4. ضبط إعدادات PostGIS:

   * تعريف جدول `buses` يحتوي على حقل `current_location` من نوع GEOGRAPHY(Point, 4326).
   * تهيئة جداول `routes` و`stops` مع حقول GEOMETRY لتخزين خطوط السير والنقاط.
5. إعداد ملف `docs/architecture.md`:

   * توثيق معمارية النظام المعيارية (Clean Architecture)، وعرض طريقة عزل البيانات لكل مستأجر (Tenant).
   * شرح بنية المجلدات الرئيسية وتدفق البيانات بين الواجهة الأمامية والخلفية.
6. حفظ التغييرات الأولية في `docs/changelog/initial-setup.md` و `docs/details/initial-setup.md`.

### الوثائق المطلوبة

* **docs/changelog/initial-setup.md:** سجل مختصر لكل تغيير تمت إضافته (إنشاء الجداول، ضبط RLS، الهيكل الأولي للمجلدات).
* **docs/details/initial-setup.md:** شرح مفصّل لكل خطوة: لماذا تم إنشاء هذه الجداول؟ كيف تعمل RLS؟ كيف يرتبط `tenant_id` بكل جدول؟
* تحديث **docs/changelog.md** العام بملخص المرحلة الأولى.

---

## المرحلة 2: نظام المصادقة وتأمين الدخول (Authentication & Security)

### الهدف الوظيفي

* تنفيذ نظام تسجيل الدخول والتسجيل الآمن مع جميع ضوابط الحماية (CSRF, Rate Limiting, 2FA).
* إعداد JWT أو Laravel Sanctum لدعم جلسات المستخدمين وربطها بالـ Multi-Tenant.

### المهام

1. **قاعدة البيانات**

   * إنشاء جدول `auth_sessions` لتخزين التوكنز (Access & Refresh Tokens) وربط كل جلسة بـ `user_id` و`tenant_id`.
   * إضافة حقول تسجيل تدقيق الدخول (IP، جهاز، زمن الدخول) في جدول `login_logs`.
2. **الواجهة الخلفية (Backend – Supabase Functions / Laravelّ إن وجدت)**

   * إعداد Supabase Auth بشكل مبدئي لدعم التسجيل بالبريد الإلكتروني/كلمة المرور:

     * التحقق من قوة كلمة المرور (≥8 رموز: حروف كبيرة/صغيرة/أرقام/رموز).
     * إظهار رسائل خطأ (ترجمة عربية/إنجليزية).
   * ضبط Rate Limiting لمحاولات تسجيل الدخول (مثلاً: 5 محاولات في 15 دقيقة مع تأخير تصاعدي).
   * تفعيل CSRF Tokens على المسارات التي تحتاج لحماية (GraphQL أو REST API).
   * إضافة دعم 2FA:

     * توليد مفتاح TOTP (Google Authenticator).
     * خيار إرسال رمز عبر البريد الإلكتروني أو SMS (Firebase Functions أو Twilio/Fawry SMS).
     * إلزام بعض الأدوار (System Admin، School Manager) بتفعيل 2FA.
   * إعداد Middleware مخصص للتحقق من صلاحيات الواجهة الخلفية: `CheckPermissions`.
   * إعداد API لإدارة الجلسات:

     * تسجيل الدخول (Login).
     * تسجيل الخروج (Logout).
     * الإنتهاء من الجلسة (Revoke Tokens).
     * منع الدخول المتزامن (اختياري): إبطال كل التوكنات القديمة عند تسجيل الدخول الجديد.
3. **الواجهة الأمامية (Frontend – React + TypeScript)**

   * تركيب حزمة `react-hook-form` مع `zod` لفورم التسجيل وتسجيل الدخول.
   * إنشاء صفحة **Login** و**Register**:

     * حقل البريد الإلكتروني وكلمة المرور، مع مؤشر قوة كلمة المرور.
     * زر إرسال مع حالة انتظار (Loading).
   * صفحة التحقق من 2FA: حقل لإدخال رمز التحقق.
   * إنشاء Hook `useAuth()` لإدارة حالة المستخدم (login, logout, refreshToken) وإرسال التوكن في الهيدر.
   * إعداد صفحة **Forgot Password** مع API لإرسال رابط إعادة تعيين كلمة المرور.
   * إضافة حمايات الواجهة (مثلاً: إخفاء الروابط عند عدم تسجيل الدخول).
4. **توثيق وتصميم**

   * كتابة تفاصيل كل نقطة في `docs/details/authentication.md`:

     * كيفية عمل Rate Limiting.
     * خطوات تفعيل 2FA.
     * شرح بنية جدول `auth_sessions` و`login_logs`.
   * تحديث `docs/changelog/authentication.md` بسجل الرموز والإعدادات الجديدة.
5. **اختبارات**

   * إضافة اختبارات الوحدة (Unit Tests) للـ Backend:

     * اختبار تسجيل الدخول بصلاحيات صحيحة/خاطئة.
     * اختبار تفعيل 2FA وعملية التحقق.
   * إضافة اختبارات التكامل (Integration) للواجهة الأمامية:

     * محاكاة تسجيل الدخول، إعادة توجيه المستخدم بعد تسجيل الدخول.

### الوثائق المطلوبة

* **docs/changelog/authentication.md**
* **docs/details/authentication.md**

---

## المرحلة 3: تصميم وإعداد نظام الأدوار والصلاحيات (RBAC & Roles Management)

### الهدف الوظيفي

* بناء نموذج بيانات مرن لإدارة الصلاحيات (Role-Based Access Control) مع تحديد نطاقات البيانات (Global, Tenant, Assigned, Children, Personal).
* إعداد واجهات لإدارة الأدوار وتوزيع الصلاحيات داخل كل مستأجر (Tenant).

### المهام

1. **قاعدة البيانات**

   * توسيع جدول `roles` ليشمل `role_scope` (Global, Tenant, Assigned, Children, Personal).
   * إنشاء جدول `permissions` مع قائمة الصلاحيات الدقيقة (BUS\_TRACK, STUDENTS\_MANAGE\_ATTENDANCE, BUSES\_MANAGE, إلخ).
   * بناء جدول وسيط `role_permission_map` لربط الأدوار بالـ permissions.
   * إنشاء جدول `user_roles` لربط المستخدمين بالأدوار مع حفظ `tenant_id`.
2. **الواجهة الخلفية**

   * كتابة Supabase Functions أو Endpoints لإدارة الصلاحيات:

     * إضافة/حذف/تعديل دور جديد.
     * ربط صلاحيات بدور (CRUD).
     * ربط مستخدم بدور داخل Tenant.
   * إعداد Middleware (أو Hook في Supabase) لفحص صلاحيات المستخدم قبل السماح بالوصول لأي مسار.
   * ضبط تعريف RLS لكل جدول بحيث:

     * `users`: يستطيع الـ System Admin رؤية جميع المستخدمين. يمكن لـ School Manager رؤية المستخدمين داخل الـ tenant الخاص به.
     * الجداول الخاصة بالطلاب/الباصات/الإشعارات: فرض فلاتر بناءً على `tenant_id` وصلاحيات المستخدم.
3. **الواجهة الأمامية**

   * إنشاء صفحة **Roles Management** في لوحة النظام المركزي (System Admin):

     * استعراض الأدوار الحالية، إنشاء دور جديد، تعديل الصلاحيات المرفوعة.
   * إنشاء صفحة **Assign Roles** لربط المستخدمين بالأدوار:

     * قائمة منسدلة بالأدوار المتوفرة في الـ tenant.
   * تطوير Hook `usePermissions()` لضبط رؤية وتفعيل/تعطيل العناصر بناءً على صلاحيات المستخدم.
   * إنشاء مكوّن `PermissionGuard` لحماية الوصول للصفحات أو الأزرار.
   * في لوحات School Manager: منع ظهور أي بيانات من مدارس أخرى تلقائيًا.
4. **توثيق وتصميم**

   * توثيق بنية الجداول (`roles`, `permissions`, `role_permission_map`, `user_roles`) في `docs/details/rbac.md`.
   * شرح نقاط RLS في `docs/details/rbac.md` مع أمثلة استعلام.
   * تحديث `docs/changelog/rbac.md` بسجل التغييرات (الجداول الجديدة، السياسات، الواجهات).
5. **اختبارات**

   * اختبارات الوحدة للـ Backend:

     * التأكد من أن المستخدم لا يستطيع الوصول لمسارات بدون صلاحية.
     * التأكد من صحة ملفات RLS.
   * اختبارات E2E للواجهة الأمامية:

     * محاولة تسجيل الدخول كمدير مدرسة والوصول لصفحة تخص System Admin يجب أن تُمنع.
     * تجربة إضافة صلاحية جديدة والتأكد من رؤية العنصر المسموح فيه.

### الوثائق المطلوبة

* **docs/changelog/rbac.md**
* **docs/details/rbac.md**

---

## المرحلة 4: إعداد بنية قاعدة البيانات التفصيلية (Database Schema & Multi-Tenant)

### الهدف الوظيفي

* تصميم الجداول التفصيلية الخاصة بالموارد الرئيسة (schools, students, parents, buses, drivers, routes, stops, notifications, attendance, maintenance, payments، إلخ) مع تضمين حقل `tenant_id` لكل جدول.
* تفعيل سياسات RLS لكل جدول لضمان عزل بيانات كل مدرسة بشكل كامل.

### المهام

1. **تصميم مخطط الجداول (Schema Design)**

   * جدول `tenants` (schools): يحتوي على الحقول الأساسية (id, name, domain/subdomain, logo\_url, primary\_color, secondary\_color, accent\_color, warning\_color, error\_color).
   * جدول `users`: (id, email, password\_hash, name\_ar, name\_en, role\_id, tenant\_id, …).
   * جدول `students`: (id, name\_ar, name\_en, parent\_id, photo\_url, tenant\_id).
   * جدول `parents`: (id, name\_ar, name\_en, phone, email, tenant\_id).
   * جدول `drivers`: (id, name\_ar, name\_en, phone, email, license\_plate, tenant\_id).
   * جدول `buses`: (id, bus\_number, capacity, current\_location GEOGRAPHY(Point), tenant\_id).
   * جدول `routes`: (id, name, bus\_id, driver\_id, tenant\_id).
   * جدول `stops`: (id, route\_id, stop\_order, location GEOGRAPHY(Point), name\_ar, name\_en).
   * جدول `attendance`: (id, student\_id, bus\_id, timestamp, status (present/absent), location GEOGRAPHY(Point), tenant\_id).
   * جدول `notifications`: (id, type, message\_ar, message\_en, recipient\_role, recipient\_id (nullable), is\_read, timestamp, tenant\_id).
   * جدول `maintenance`: (id, bus\_id, description, scheduled\_date, status, tenant\_id).
   * جدول `payments`: (id, student\_id, amount, type (subscription/penalty), method, status, timestamp, tenant\_id).
2. **إنشاء الجداول وتفعيل RLS**

   * في Supabase Dashboard أو SQL Editor: تنفيذ سكربت إنشاء الجداول أعلاه.
   * لكل جدول:

     ```sql
     ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;
     CREATE POLICY "tenant_access_only" ON table_name
       USING (tenant_id = auth.uid());  -- auth.uid() يُفيد عندما تكون الـ JWT تحتوي on tenant_id 
     ```
   * مثال: لجدول `students`:

     ```sql
     CREATE POLICY "tenant_students" ON students
       USING (tenant_id = (SELECT tenant_id FROM users WHERE users.id = auth.uid()));
     ```
   * التأكد من أن كل استعلام يرفق `tenant_id` تلقائيًا من الـ JWT أو Session.
3. **توليد ملف `database-schema.md`**

   * كتابة وصف تفصيلي للجداول والعلاقات بينها.
   * توثيق نوع الحقول الجغرافية وشرح كيفية استخدامها مع PostGIS.
4. **توثيق وتصميم**

   * **docs/details/database.md**: شرح كل جدول (الغرض، الحقول، العلاقات، سياسات RLS).
   * **docs/changelog/database.md**: سجل إنشاء الجداول وتفعيل RLS.
5. **اختبارات قاعدة البيانات**

   * كتابة سكربتات اختبار للتأكد من أن المستخدم (tenant) لا يستطيع رؤية بيانات من طالب/باص/مدرسة أخرى.
   * استخدام Postman أو Insomnia لإرسال طلبات واجهة API ومحاولة تجاوز الـ RLS (مثال: إدخال `tenant_id` يدويًا).

### الوثائق المطلوبة

* **docs/changelog/database.md**
* **docs/details/database.md**

---

## المرحلة 5: إعداد بنية واجهات المستخدم الأساسية (Frontend Scaffolding & Theming)

### الهدف الوظيفي

* إنشاء بنية الواجهة الأمامية (React + TypeScript) مع تركيب TailwindCSS وRadix UI وLucide React.
* ضبط نظام الألوان (Design Tokens) والخطوط (Cairo) ودعم الوضع الداكن (Dark Mode) والـ RTL.

### المهام

1. **تهيئة مشروع Vite + React + TypeScript**

   * تشغيل:

     ```bash
     npm create vite@latest school-system-frontend --template react-ts
     cd school-system-frontend
     npm install
     ```
   * تركيب TailwindCSS:

     ```bash
     npm install -D tailwindcss postcss autoprefixer
     npx tailwindcss init -p
     ```
   * تحديث `tailwind.config.js` بالكود التالي:

     ```js
     module.exports = {
       content: ["./src/**/*.{js,ts,jsx,tsx}"],
       darkMode: 'class', // يدعم الوضع الداكن
       theme: {
         extend: {
           colors: {
             primary: '#3b82f6',
             secondary: '#14b8a6',
             accent: '#10b981',
             warning: '#f59e0b',
             error: '#ef4444',
           },
           fontFamily: {
             sans: ['Cairo', 'sans-serif'],
           },
         },
       },
       plugins: [],
     }
     ```
   * إضافة رابط خط Cairo في `index.html`:

     ```html
     <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet" />
     ```
2. **ضبط هيكل الـ UI Components**

   * إنشاء مجلد `src/ui/components/` ويحتوي على مكونات أساسية (Button.tsx, Input.tsx, Modal.tsx).
   * تركيب Radix UI:

     ```bash
     npm install @radix-ui/react-dialog @radix-ui/react-select @radix-ui/react-tabs
     ```
   * تركيب Lucide React:

     ```bash
     npm install lucide-react
     ```
   * في كل مكوّن (مثل Button): استخدام الألوان المعرّفة في Tailwind (مثلاً: `bg-primary hover:bg-primary-dark`, `text-white`, `rounded-2xl`, `p-2`).
3. **إعداد الثيم والدعم ثنائي اللغة (RTL / LTR)**

   * تركيب مكتبة i18n:

     ```bash
     npm install i18next react-i18next i18next-browser-languagedetector
     ```
   * إعداد ملف `i18n.ts` في `src/`: تلفيق الترجمة العربية والإنجليزية.
   * إضافة Context لإدارة اللغة والاتجاه (RTL / LTR).
   * تحديث `index.tsx` لتغليف `<App />` بترانزيشن اللغات.
4. **تفعيل Dark Mode**

   * في `tailwind.config.js` اختر `darkMode: 'class'`.
   * إنشاء زر Toggle في مكوّن `ThemeToggle.tsx` للتبديل بين الوضع الفاتح والداكن من خلال إضافة/إزالة `class="dark"` على عنصر الجذر (`<html>`).
5. **هيكلة مجلد الصفحات (Pages)**

   ```
   src/
   ├── pages/
   │   ├── Login.tsx
   │   ├── Register.tsx
   │   ├── Dashboard/  
   │   │   ├── SystemAdmin.tsx
   │   │   ├── SchoolAdmin.tsx
   │   │   ├── Driver.tsx
   │   │   ├── Parent.tsx
   │   │   └── Student.tsx
   │   ├── Users/
   │   │   ├── UsersList.tsx
   │   │   └── UserForm.tsx
   │   ├── Students/
   │   │   ├── StudentsList.tsx
   │   │   └── StudentForm.tsx
   │   ├── Buses/
   │   │   ├── BusesList.tsx
   │   │   └── BusForm.tsx
   │   ├── Routes/
   │   │   ├── RoutesList.tsx
   │   │   └── RouteForm.tsx
   │   ├── Attendance/
   │   │   ├── AttendanceList.tsx
   │   │   └── AttendanceScanner.tsx
   │   ├── Tracking/
   │   │   └── LiveTracking.tsx
   │   ├── Notifications/
   │   │   └── NotificationsList.tsx
   │   ├── Reports/
   │   │   ├── AttendanceReport.tsx
   │   │   └── PerformanceReport.tsx
   │   └── Settings/
   │       ├── SystemSettings.tsx
   │       └── ProfileSettings.tsx
   └── App.tsx
   ```
6. **توثيق وتصميم**

   * كتابة **docs/details/frontend-setup.md**: شرح خطوات ضبط Tailwind، Radix UI، Lucide، i18n، Dark Mode، بنية المجلدات.
   * تحديث **docs/changelog/frontend-setup.md** بتفصيل الإضافات (ملفات config، الحزم المركبة، المجلدات المنشأة).
7. **اختبارات**

   * إضافة اختبار بسيط باستخدام React Testing Library للتأكد من أنّ صفحة Login تُعرض بشكل صحيح.

### الوثائق المطلوبة

* **docs/changelog/frontend-setup.md**
* **docs/details/frontend-setup.md**

---

## المرحلة 6: إعداد إدارة المستخدمين والمدارس (Users & Tenants Management)

### الهدف الوظيفي

* إنشاء واجهات CRUD للمستخدمين ضمن كل Tenant (مدير، سائق، ولي أمر، طالب).
* إنشاء واجهات CRUD للـ `tenants` (المدارس) من قبل System Admin، مع ربط كل مستخدم بالـ tenant الصحيح.

### المهام

1. **قاعدة البيانات**

   * التأكد من وجود جدول `tenants` وحقول `domain` أو `subdomain` ليكون لكل مدرسة نطاق مستقل.
   * إضافة حقول في جدول `users`: `phone_number`, `profile_photo_url`.
2. **الواجهة الخلفية**

   * إنشاء Endpoints لإدارة الـ Tenants (CRUD):

     * `GET /api/tenants` (System Admin فقط).
     * `POST /api/tenants` لإنشاء مدرسة جديدة مع تخصيص شعار وألوان مبدئية.
     * `PATCH /api/tenants/:id` لتحديث بيانات المدرسة (اسم، شعار، ألوان).
     * `DELETE /api/tenants/:id` لحذف مدرسة (مع وضع حماية لعدم حذف العد الكبير تلقائيًا).
   * إنشاء Endpoints لإدارة المستخدمين:

     * `GET /api/users?tenant_id=...` (School Admin أو أعلى فقط).
     * `POST /api/users` لإنشاء مستخدم جديد (يتطلب Role + Tenant + بيانات أساسية).
     * `PATCH /api/users/:id` لتعديل بيانات المستخدم (مثل تغيير الدور أو إعادة تعيين كلمة المرور).
     * `DELETE /api/users/:id` لحذف المستخدم (مع تأكيد إلغاء صلاحياته وسجله).
   * تضمين التحقق من صلاحيات المستخدم الحالي قبل كل عملية (System Admin فقط يستطيع إدارة كل المدارس، School Admin يدير فقط مستخدمي مدرسته).
3. **الواجهة الأمامية**

   * **صفحة Tenants Management** (Dashboard System Admin):

     * جدول يعرض قائمة المدارس (اسم، نطاق، عدد المستخدمين، الحالة).
     * زر “إنشاء مدرسة جديدة” يفتح Modal مع فورم البيانات: الاسم، الشعار، الألوان (مع معاينة حية).
     * في كل صف: أزرار “تعديل” و“حذف” (مع تأكيد عبر Modal).
   * **صفحة Users Management** (Dashboard School Admin):

     * جدول يعرض مستخدمي المدرسة (الإسم، الدور، البريد الإلكتروني، حالة 2FA مفعل/غير مفعل).
     * زر “إضافة مستخدم” لفتح Form: الاسم بالعربي/إنجليزي، البريد، رقم الهاتف، رفع صورة، اختيار دور (مدير مدرسة، سائق، ولي أمر، طالب، مشرف حركة).
     * إمكانية البحث والتصفية حسب الدور واسم المستخدم.
     * زر تعديل لحفظ التغييرات، وزر حذف (مع رسالة تأكيد).
   * ربط العمليات مع Hook `usePermissions()` و`PermissionGuard` لمنع الوصول غير المصرح به.
4. **توثيق وتصميم**

   * **docs/details/tenants-users.md**: شرح بنية جداول `tenants` و`users`، flow الخاص بإنشاء مستخدم جديد وربطه بالمدرسة.
   * **docs/changelog/tenants-users.md**: سجل إنشاء Endpoints وواجهات المستخدم.
5. **اختبارات**

   * اختبارات Integration للـ Frontend:

     * إنشاء مدرسة جديدة ثم التحقق من ظهورها في القائمة.
     * إنشاء مستخدم جديد وتمكينه من تسجيل الدخول.
   * اختبارات E2E: استخدام Cypress (إن وجد) لمحاكاة System Admin المُنشئ للمدرسة وSchool Admin المُنشئ للمستخدمين.

### الوثائق المطلوبة

* **docs/changelog/tenants-users.md**
* **docs/details/tenants-users.md**

---

## المرحلة 7: إدارة الطلاب (Students Management)

### الهدف الوظيفي

* توفير واجهة CRUD لإدارة بيانات الطلاب وربطهم بأولياء الأمور والمسارات.
* دعم رفع صور الطلاب وملفاتهم الشخصية.

### المهام

1. **قاعدة البيانات**

   * إضافة حقل `files` (JSON array) في جدول `students` لتخزين روابط الملفات (مثل: شهادات طبية، أوراق رسمية).
   * تأكيد وجود علاقة Foreign Key بين `students.parent_id` و`parents.id`.
2. **الواجهة الخلفية**

   * Endpoints إدارة الطلاب:

     * `GET /api/students?tenant_id=...` (School Admin أو أعلى).
     * `POST /api/students` لإنشاء طالب جديد مع استلام Multipart Form Data (الصورة والملفات).
     * `PATCH /api/students/:id` لتحديث بيانات الطالب والملف الشخصي.
     * `DELETE /api/students/:id` لحذف الطالب (مع قرارات حول نقل ملفات إيجابية قبل الحذف).
   * عند استلام صورة/ملفات: رفعها إلى storage (Supabase Storage أو S3) ثم حفظ الروابط في العمود `photo_url` و`files`.
3. **الواجهة الأمامية**

   * **صفحة StudentsList.tsx**:

     * جدول يحتوي على الأعمدة: الصورة المصغرة، اسم الطالب (Ar/En)، اسم ولي الأمر، المسار الحالي (إن وجد)، الحالة (نشط/غير نشط).
     * أزرار “تعديل” و“حذف”، وزر “إضافة طالب” أعلى الجدول.
     * إمكانية تصفية الطلاب حسب الصف/المسار.
   * **مكوّن StudentForm.tsx**:

     * فورم يشمل: الاسم بالعربي والإنجليزي (حقلي نص)، اختيار ولي الأمر من قائمة منسدلة (Parent Selector)، رفع صورة (Image Uploader)، رفع ملفات إضافية (Multi-file Uploader)، اختيار المسار (Route Selector)، حقل لاختيار رقم الجلوس (Seat Number).
     * تحقق Form عبر `zod` (مثل: الاسم لا يمكن أن يكون فارغًا، حجم الصورة ≤ 2MB).
     * زر حفظ/تحديث يستدعي Endpoints المناسبة.
4. **توثيق وتصميم**

   * **docs/details/students.md**: شرح الحقول الجديدة (`files`)، طريقة رفع الملفات والتخزين، علاقة الطلاب بالأولياء والمسارات.
   * **docs/changelog/students.md**: سجل الإنشاء والتعديلات الخاصة بجداول الطلاب والواجهات.
5. **اختبارات**

   * اختبار ارتفاع/انخفاض الصور للطالب (Image Upload).
   * اختبار ربط الطالب بولي أمر موجود مسبقًا.
   * اختبار حذف طالب والتأكد من إزالة ملف الصورة من التخزين إن لم يعد مستخدمًا.

### الوثائق المطلوبة

* **docs/changelog/students.md**
* **docs/details/students.md**

---

## المرحلة 8: إدارة أولياء الأمور (Parents Management)

### الهدف الوظيفي

* إنشاء واجهة CRUD لإدارة بيانات أولياء الأمور وربطهم بالطلاب.
* دعم إرسال إشعارات خاصة بولي الأمر.

### المهام

1. **قاعدة البيانات**

   * تأكد من وجود جدول `parents` مع الحقول: `name_ar`, `name_en`, `phone`, `email`, `tenant_id`.
   * إضافة حقل `students_ids` (Array of UUID) إن دعت الحاجة.
2. **الواجهة الخلفية**

   * Endpoints إدارة أولياء الأمور:

     * `GET /api/parents?tenant_id=...` (School Admin أو أعلى).
     * `POST /api/parents` لإنشاء ولي أمر جديد.
     * `PATCH /api/parents/:id` لتحديث البيانات.
     * `DELETE /api/parents/:id` للحذف (مع مراعاة إعادة ربط الطلاب إن لزم الأمر).
   * إضافة Endpoint خاص بإرسال إشعار (Notification) لولي أمر: `POST /api/notifications/parent/:parent_id`.
3. **الواجهة الأمامية**

   * **صفحة ParentsList.tsx**:

     * جدول يعرض: اسم ولي الأمر بالعربي/الإنجليزي، رقم الهاتف، البريد الإلكتروني، عدد الأبناء (Students Count).
     * زر “إرسال إشعار” في كل صف لفتح نافذة صغيرة (Modal) لكتابة الرسالة وإرسالها.
     * أزرار “تعديل” و“حذف” و“عرض الأبناء” (تفتح قائمة مؤقتة تضم أسماء الأبناء).
   * **مكوّن ParentForm.tsx**:

     * فورم لحفظ البيانات الأساسية (الأسم، رقم الهاتف، البريد، رفع صورة إن دعت الحاجة).
     * عند الحفظ: تحديث العقود (Contracts) إن وجدت أو ربط الطالب تلقائيًا إذا تم إرسال معرفه.
4. **توثيق وتصميم**

   * **docs/details/parents.md**: شرح بنية جدول `parents` وطريقة الربط بـ`students`.
   * **docs/changelog/parents.md**: سجل إضافة جدول وواجهات والربط.
5. **اختبارات**

   * اختبار إرسال إشعار لولي أمر والتأكد من إضافته إلى جدول `notifications`.
   * اختبار حذف ولي أمر مع وجود طلاب مرتبطين (يجب منع الحذف أو إعادة ربط الطلاب).

### الوثائق المطلوبة

* **docs/changelog/parents.md**
* **docs/details/parents.md**

---

## المرحلة 9: إدارة السائقين والحافلات (Drivers & Buses Management)

### الهدف الوظيفي

* إنشاء واجهة CRUD لإضافة السائقين وربطهم بالحافلات.
* إنشاء واجهة CRUD لإدارة الحافلات وربطها بخطوط السير (Routes).

### المهام

1. **قاعدة البيانات**

   * جدول `drivers`: تأكد من الحقول: `name_ar`, `name_en`, `phone`, `email`, `license_number`, `tenant_id`.
   * جدول `buses`: تأكد من الحقول: `bus_number`, `capacity`, `current_location GEOGRAPHY(Point)`, `driver_id` (nullable), `tenant_id`.
   * جدول وسيط `driver_bus_map` إذا رغبت بفصل العلاقة إلى Many-to-Many (Driver يمكنه قيادة حافلة واحدة في الوقت الجاري، ولكن قد يغيّر الحافلة لاحقًا).
2. **الواجهة الخلفية**

   * Endpoints إدارة السائقين:

     * `GET /api/drivers?tenant_id=...` (School Admin أو أعلى).
     * `POST /api/drivers` لإنشاء سائق جديد.
     * `PATCH /api/drivers/:id` لتعديل البيانات (تغيير تفاصيل الاتصال، إلخ).
     * `DELETE /api/drivers/:id` (مع إعادة ربط الحافلة إن لزم الأمر).
   * Endpoints إدارة الحافلات:

     * `GET /api/buses?tenant_id=...`.
     * `POST /api/buses` لإضافة حافلة جديدة (يتضمن رفع بيانات البطاقة والتصاريح إن وجدت).
     * `PATCH /api/buses/:id` لتحديث معلومات الحافلة وربط السائق (`driver_id`).
     * `DELETE /api/buses/:id` بحماية إذا كانت الحافلة مرتبطة بمسار حالي.
   * ضبط اتصال API بين `drivers` و`buses`: عند تعديل `driver_id` في `/api/buses/:id`، يتم تحديث علاقة السائق تلقائيًا.
3. **الواجهة الأمامية**

   * **صفحة DriversList.tsx**:

     * جدول يعرض: صورة السائق (إن وجدت)، الاسم (Ar/En)، رقم الهاتف، الحافلة المعينة (Bus Number)، حالة النشاط (Active/Inactive).
     * زر “تعيين حافلة” في كل صف لفتح Modal يختار الحافلة من قائمة منسدلة.
     * زر “إضافة سائق” يفتح `DriverForm.tsx`.
   * **مكوّن DriverForm.tsx**:

     * فورم يتضمن: الاسم بالعربي/الإنجليزي، البريد الإلكتروني، الهاتف، رقم الرخصة، رفع صورة شخصية.
     * تحقق Form عبر `zod`.
   * **صفحة BusesList.tsx**:

     * جدول يعرض: رقم الحافلة، السائق الحالي (Driver Name)، الموقع الحالي (عرض مختصر)، السعة (Capacity).
     * زر “تعديل” يفتح `BusForm.tsx`، وزر “حذف” (مع تحقق إن كانت مربوطة بالمسارات).
     * زر “إضافة حافلة” لفتح `BusForm.tsx`.
   * **مكوّن BusForm.tsx**:

     * فورم: رقم الحافلة (Bus Number)، السعة (Capacity)، اختيار السائق من قائمة منسدلة، رفع مستندات الحافلة (صور/إثباتات)، تحديد الموقع الابتدائي عبر خريطة تفاعلية (Mapbox Picker).
4. **توثيق وتصميم**

   * **docs/details/drivers-buses.md**: شرح بنية جداول `drivers` و`buses`، كيفية الربط بينهما، وصف حقول GEOGRAPHY للمواقع.
   * **docs/changelog/drivers-buses.md**: سجل إنشاء الجداول والواجهات والربط.
5. **اختبارات**

   * التحقق من ربط سائق بحافلة جديدة.
   * اختبار حذف سائق له رحلة جارٍ تشغيلها (يجب منع الحذف أو نقل الرحلة أولًا).
   * اختبار تحديد موقع الحافلة عبر خريطة Mapbox والتأكد من حفظ الإحداثيات بشكل صحيح.

### الوثائق المطلوبة

* **docs/changelog/drivers-buses.md**
* **docs/details/drivers-buses.md**

---

## المرحلة 10: إدارة المسارات (Routes & Stops Management)

### الهدف الوظيفي

* توفير واجهة CRUD لإنشاء وتعديل المسارات، تحديد نقاط التوقف باستخدام خريطة تفاعلية.
* ربط كل مسار بحافلة واحدة وسائق واحد وتحديد جدول زمني للوصول لكل نقطة.

### المهام

1. **قاعدة البيانات**

   * جدول `routes`:

     * الحقول: `name`, `bus_id`, `driver_id`, `start_time`, `end_time`, `tenant_id`.
   * جدول `stops`:

     * الحقول: `route_id`, `stop_order` (ترتيب النقاط)، `location GEOGRAPHY(Point)`, `name_ar`, `name_en`, `arrival_time_estimate` (التوقيت التقديري).
   * جدول `route_schedule` إن دعت الحاجة للتعامل مع جداول زمنية متعددة لكل يوم في الأسبوع.
2. **الواجهة الخلفية**

   * Endpoints المسارات:

     * `GET /api/routes?tenant_id=...`.
     * `POST /api/routes` لإنشاء مسار جديد مع استقبال JSON يحتوي على قائمة النقاط (stops).
     * `PATCH /api/routes/:id` لتحديث بيانات المسار ونقاط التوقف.
     * `DELETE /api/routes/:id` (مع منع الحذف إذا كان هناك رحلات نشطة).
   * Endpoints النقاط:

     * `GET /api/stops?route_id=...`.
     * `POST /api/stops` لإضافة نقطة جديدة.
     * `PATCH /api/stops/:id` لتحديث الموقع أو الاسم.
     * `DELETE /api/stops/:id`.
3. **الواجهة الأمامية**

   * **صفحة RoutesList.tsx**:

     * جدول يعرض: اسم المسار، الحافلة المعينة، السائق، عدد النقاط، الحالة (Active/Inactive).
     * زر “إضافة مسار” لفتح نافذة `RouteForm.tsx`.
     * زر “تعديل” لكل مسار لفتح `RouteForm.tsx` مع البيانات المحملة.
   * **مكوّن RouteForm.tsx**:

     * حقول إدخال: اسم المسار، اختيار الحافلة (Bus Selector)، اختيار السائق (Driver Selector)، تحديد وقت بداية المسار ونهايته.
     * منطقة خريطة تفاعلية (Mapbox Map) لعرض النقاط الحالية، وزر “إضافة نقطة” عند الضغط على الخريطة يُنشئ Marker جديد.
     * عرض الجدول الزمني التقديري (Arrival Time Estimate) بناءً على سرعة متوسطة مُحددة.
     * قائمة جانبية (Sidebar) تعرض النقاط بشكل مرتب (Stop Order) مع إمكانية سحب السطر لتغيير الترتيب (Drag & Drop).
   * **مكوّن StopItem.tsx** (داخل RouteForm) لكل نقطة:

     * يعرض اسم النقطة بالعربي/إنجليزي، زر تعديل الاسم، زر حذف النقطة، حقل إدخال لتعديل وقت الوصول التقديري.
4. **توثيق وتصميم**

   * **docs/details/routes-stops.md**: شرح بنية جداول `routes` و`stops`، مثال على هيكل JSON لنقاط المسار، كيفية استخدام Mapbox لإنشاء نقاط.
   * **docs/changelog/routes-stops.md**: سجل إنشاء الجداول والواجهات والربط.
5. **اختبارات**

   * اختبار إنشاء مسار جديد مع 3 نقاط متتالية على الخريطة والتأكد من ظهورها في الجدول.
   * اختبار تعديل ترتيب النقاط والتأكد من تحديث `stop_order` في قاعدة البيانات.
   * اختبار حذف نقطة والتأكد من اختفاء Marker من الخريطة وتحديث الجدول.

### الوثائق المطلوبة

* **docs/changelog/routes-stops.md**
* **docs/details/routes-stops.md**

---

## المرحلة 11: نظام التتبع المباشر (Real-Time GPS Tracking)

### الهدف الوظيفي

* توفير واجهة عرض مباشر لمواقع الحافلات على الخريطة، مع دعم Geo-fencing وإنذارات الخروج عن المسار.

### المهام

1. **قاعدة البيانات**

   * تأكد من وجود حقل `current_location` في جدول `buses` مُحدّث بشكل لحظي.
   * إنشاء جدول `bus_locations_history` لتخزين تاريخ مواقع الحافلة (bus\_id, location GEOGRAPHY(Point), timestamp, tenant\_id).
2. **الواجهة الخلفية**

   * إعداد Supabase Realtime أو WebSocket عبر Supabase Functions لنشر تحديثات الموقع آنيًا:

     * كلما تحرك السائق (من التطبيق الجوال أو لوحة السائق)، يتم إرسال موقعه إلى Endpoint: `POST /api/buses/:id/location` مع `latitude`, `longitude`.
     * في Endpoint:

       1. تحديث حقل `current_location` في جدول `buses`.
       2. إدخال سجل جديد في `bus_locations_history`.
       3. نشر الحدث عبر قناة Realtime (supabase.from(`bus_locations:tenant_id=eq.<tenant_id>`).on('UPDATE', …)).
   * إضافة منطق Geo-fencing في Backend:

     * لكل مسار، بناء Polygon بسيط حول كل نقطة (Buffer بحسب نصف قطر محدد، مثلاً 50 متر).
     * عند وصول تحديث جديد للموقع، فحص إذا كانت الحافلة خرجت عن المسار الأمامي (LineString المكوّن من نقاط المسار).
     * إذا خرجت: إنشاء إشعار طارئ (type = "geofence\_alert") في جدول `notifications` مخصّص للسائق ومدير المدرسة.
3. **الواجهة الأمامية**

   * **صفحة LiveTracking.tsx** (لوحة School Admin وDriver):

     * عرض خريطة Mapbox تفاعلية بتمركز افتراضي (Zoom مناسب).
     * تحميل بيانات المسار (LineString) وعرضها على الخريطة مع لون مميز (primary color).
     * إنشاء Layer خاص بالـ Bus Marker (Icon صغير) يتحرك بناءً على الإحداثيات الآنية المرسلة عبر WebSocket/Supabase Realtime.
     * عند استقبال حدث خروج عن المسار (geofence\_alert)، عرض تنبيه باللون البرتقالي (Warning) على الشاشة وفي قسم الإشعارات.
   * **مكوّن BusHistoryMap.tsx**:

     * يعرض خريطة مع مسار الحافلة التاريخي في اليوم الحالي (LineString مكوّن من `bus_locations_history`).
     * شريط زمني (Slider) يمكن السحب للتنقل بين الأوقات، ويُظهر موقع الحافلة عند كل نقطة زمنية.
4. **توثيق وتصميم**

   * **docs/details/gps-tracking.md**: شرح كيفية عمل Realtime باستخدام Supabase، بنية جدول `bus_locations_history`، منطق Geo-fencing (مع مثال SQL).
   * **docs/changelog/gps-tracking.md**: سجل إنشاء الجداول وواجهات Realtime والوظائف.
5. **اختبارات**

   * محاكاة إرسال تحديثات الموقع عبر Postman للتأكد من وصولها إلى الخريطة مباشرة.
   * اختبار خروج الحافلة عن المسار والتحقق من إنشاء الإشعار المناسب والعرض في الواجهة.

### الوثائق المطلوبة

* **docs/changelog/gps-tracking.md**
* **docs/details/gps-tracking.md**

---

## المرحلة 12: نظام الحضور والانصراف (Attendance Management)

### الهدف الوظيفي

* تنفيذ طريقة تسجيل حضور وانصراف الطلاب باستخدام QR Code أو زر في تطبيق السائق، مع حفظ معلومات الزمن والموقع.
* إنشاء واجهة لإدارة سجلات الحضور والانصراف من قبل School Admin وولي الأمر.

### المهام

1. **قاعدة البيانات**

   * جدول `attendance`:

     * الحقول: `id`, `student_id`, `bus_id`, `timestamp`, `status` (`boarded` / `alighted`), `location GEOGRAPHY(Point)`, `tenant_id`.
   * جدول `qr_codes` (إن دعت الحاجة) لتخزين رموز QR المرتبطة بكل طالب: `student_id`, `qr_value` (مثلاً UUID)، `expired_at` (اختياري).
2. **الواجهة الخلفية**

   * إعداد Endpoint لإنشاء رمز QR لكل طالب:

     * `POST /api/attendance/generate-qr/:student_id`.
     * يولّد رمزًا فريدًا (UUID) محفوظًا في جدول `qr_codes`.
   * Endpoint لتسجيل حضور/انصراف عبر QR:

     * `POST /api/attendance/scan-qr` يستقبل { `qr_value`, `bus_id`, `location` }.
     * في حال كان `qr_value` صالح ولم تنته صلاحيته:

       * إنشاء سجل جديد في `attendance` بالحالة `boarded` إن كانت أول مرة، أو `alighted` إن كان الطالب حاليًا موجودًا على متن الحافلة (التبديل بين الحالتين تلقائيًا).
       * إرسال إشعار لولي الأمر (نوع `attendance_update`) وفي حالة تأخير الباص يرسل إشعار متأخر.
   * Endpoint لتسجيل حضور/انصراف عبر زر (Driver App):

     * `POST /api/attendance/toggle` يستقبل { `student_id`, `bus_id`, `location` }.
     * تطبق نفس المنطق أعلاه (تبديل الحالة بلوحة السائق).
   * Endpoint لاسترجاع سجلات الحضور:

     * `GET /api/attendance?student_id=...&date=YYYY-MM-DD`.
3. **الواجهة الأمامية**

   * **صفحة AttendanceScanner.tsx** (لوحة السائق):

     * عرض كاميرا الجهاز (إن كان رابطًا للتطبيق المحمول) لقراءة QR Code.
     * زر يدوي “تسجيل حضور/انصراف” في حالة عدم وجود QR أو تعذر القراءة.
     * بعد النجاح: عرض إشعار صغير (Toast) باللون الأخضر (Accent).
   * **صفحة AttendanceList.tsx** (School Admin وParent):

     * جدول يعرض: اسم الطالب، حالة الحضور (Boarded/Alighted)، وقت الحضور، وقت الانصراف (إن وجد)، تاريخ اليوم.
     * إمكانية اختيار تاريخ محدد وعرض السجلات الخاصة به.
     * تصدير التقرير بصيغة PDF/Excel (استخدام مكتبة `jspdf` أو `html2canvas`).
   * **مكوّن QRCodeGenerator.tsx** (School Admin):

     * فورم لإنشاء رمز QR لكل طالب قابل للطباعة (طباعة بطاقة تعريف بالرمز مع اسم الطالب).
   * **مكوّن QRCodeComponent.tsx**: لعرض رمز QR على شاشة ولي الأمر/طالب (إذا كان للدخول للمنصة أو لتوحيد التوثيق).
4. **توثيق وتصميم**

   * **docs/details/attendance.md**: شرح جدول `attendance` و`qr_codes`، منطق تغيير الحالة، إرسال الإشعارات، مثال على Payload للقراصنة.
   * **docs/changelog/attendance.md**: سجل إنشاء الجداول والواجهات والـ Endpoints.
5. **اختبارات**

   * اختبار مسح رمز QR صالح وغير صالح.
   * اختبار تبديل الحالة عند الضغط على زر الحضور في واجهة السائق.
   * اختبار ظهور الإشعار للولي الأمر في حالة حضور الطالب أو تأخر الباص.

### الوثائق المطلوبة

* **docs/changelog/attendance.md**
* **docs/details/attendance.md**

---

## المرحلة 13: نظام الإشعارات المتقدم (Advanced Notifications)

### الهدف الوظيفي

* تصميم نظام إشعارات فوري يدعم Push + Email + SMS وفقًا للصلاحيات والأحداث.
* إرسال إشعارات عند حالات متعددة: وصول الباص، تأخر الباص، حضور/انصراف الطالب، حوادث الطوارئ، تذكير بموعد الصيانة، تقييمات السائق، تنبيه خروج عن المسار (مكتمل في مرحلة التتبع).

### المهام

1. **قاعدة البيانات**

   * جدول `notifications`:

     * الحقول: `id`, `type`, `title_ar`, `title_en`, `message_ar`, `message_en`, `recipient_role`, `recipient_id` (User ID إن وُجد، أو Null ليُرسل لجميع أصحاب الدور)، `is_read`, `metadata` (JSON للاحتفاظ بأي بيانات إضافية مثل `bus_id`, `route_id`)، `created_at`, `tenant_id`.
   * جدول `notification_logs`: لتتبع إرسال الإشعارات (نوع القناة، الحالة: تم الإرسال / فشل، response\_data).
2. **الواجهة الخلفية**

   * إعداد Supabase Functions أو Serivce (Deno) لإرسال الإشعارات:

     * دمج OneSignal أو Firebase Cloud Messaging (FCM) للإشعارات الفورية.
     * دمج خدمة SMS (SMSMisr أو Twilio أو Fawry) لإرسال رسائل SMS للآباء والسائقين في حالات الطوارئ أو التذكيرات.
     * إعداد SMTP أو SendGrid لإرسال البريد الإلكتروني (Email).
   * إنشاء Function عامة: `sendNotification(payload)` يقبل النوع والقنوات والمستلمين ويقوم بما يلي:

     1. حفظ السجل في جدول `notifications`.
     2. إرسال الإشعار عبر القنوات المختارة (Push/Email/SMS).
     3. تسجيل النتيجة في `notification_logs`.
   * Endpoints استدعاء الإشعار:

     * `POST /api/notifications/send` (يمكن استدعاؤها من أي Module آخر عند وقوع حدث).
     * `GET /api/notifications?recipient_id=...&is_read=boolean` لعرض إشعارات المستخدم.
     * `PATCH /api/notifications/:id/read` لتعليم الإشعار كمقروء.
3. **الواجهة الأمامية**

   * **صفحة NotificationsList.tsx**:

     * تظهر للإدمن، School Admin، ولي الأمر، السائق، بناءً على الدور.
     * جدول أو لائحة بالإشعارات الأحدث أولًا (مرتبة نزوليًا)، مع أيقونة صغيرة توضح النوع (📧 لبريد إلكتروني، 🔔 لإشعار فوري، ✉️ للـ SMS).
     * إمكانية فرز/تصفية حسب التاريخ أو النوع.
     * عند النقر على إشعار: فتح Modal فيه التفاصيل (العنوان، الرسالة، بيانات مرتبطة مثل اسم الباص ورقم المسار).
   * **مكوّن NotificationBell.tsx** (في Header):

     * أيقونة جرس تعرض عدد الإشعارات غير المقروءة.
     * عند الضغط، تظهر قائمة منسدلة (Dropdown) بآخر 5 إشعارات.
   * **إرسال إشعارات من واجهات أخرى**:

     * على سبيل المثال:

       * **LiveTracking.tsx**: حين يتأخر الباص 5 دقائق عن الوقت المتوقع: استدعاء `sendNotification({ type: 'bus_delay', recipient_role: 'parent', ... })`.
       * **AttendanceScanner.tsx**: عند تسجيل حضور/انصراف: `sendNotification({ type: 'attendance_update', recipient_id: parent_id, ... })`.
       * **Maintenance Module** (لاحقًا): تذكير قبل موعد الصيانة بيوم.
4. **توثيق وتصميم**

   * **docs/details/notifications.md**: بنية جدول `notifications` و`notification_logs`، أمثلة Payload لإرسال إشعار، إعدادات OneSignal/FCM/Twilio/SMTP.
   * **docs/changelog/notifications.md**: سجل إعداد الجداول والواجهات والدمج مع الخدمات الخارجية.
5. **اختبارات**

   * اختبار إرسال إشعار من جهة الواجهة الخلفية والتأكد من وصوله عبر إحدى القنوات (Push / Email / SMS).
   * اختبار تلقي الإشعار في الواجهة الأمامية (عدد غير المقروءة يظهر بشكل صحيح، ويختفي عند القراءة).
   * اختبار إشعار تأخر الباص: محاكاة تأخر 10 دقائق والتأكد من إنشاء الإشعار للولي الأمر.

### الوثائق المطلوبة

* **docs/changelog/notifications.md**
* **docs/details/notifications.md**

---

## المرحلة 14: لوحات التحكم (Dashboards)

### الهدف الوظيفي

* إنشاء لوحات تحكم مختلفة لكل دور (System Admin, School Admin, Driver, Parent, Student, مشرف حركة).
* عرض إحصائيات ومعلومات مجمّعة في الوقت الفعلي (Realtime Stats) باستخدام WebSocket أو Supabase Realtime + Redis.

### المهام

1. **تصميم واجهات الـ Dashboards**

   * **System Admin Dashboard (`dashboards/super-admin/SuperAdmin.tsx`)**:

     * عدد المدارس النشطة (Active Tenants).
     * نسبة تغطية المسارات (عدد المدارس التي لديها مسارات / إجمالي المدارس).
     * عدد الحافلات قيد التشغيل (Active Buses).
     * إحصائيات عامة عن الحضور (Total Attendance Today).
     * خريطة عالمية بسيطة (Mapbox) توضح مواقع المدن التي تعمل فيها المدارس.
   * **School Admin Dashboard (`dashboards/school-admin/SchoolAdmin.tsx`)**:

     * نسب الحضور اليومي/الأسبوعي/الشهري (Bar Chart باستخدام Recharts).
     * تغطية خطوط السير (عدد الطلاب في كل مسار).
     * تتبع مباشر للحافلات التابعة للمدرسة.
     * آخر 5 إشعارات صادرة.
     * جدول سريع بحالات الصيانة المقبلة (Maintenance Schedule).
     * إحصائيات أداء السائقين (متوسط درجة التقييم).
   * **Driver Dashboard (`dashboards/driver/Driver.tsx`)**:

     * مسار الرحلة الحالي (LineString on Mapbox).
     * قائمة الطلاب المؤدين للرحلة (Student List) مع حالة الحضور (Boarded/Alighted).
     * زر “بدء الرحلة” و“إنهاء الرحلة” لتسجيل وقت الانطلاق والوصول.
     * حالة الصيانة الحالية (إشعار بموعد الصيانة القادم).
   * **Parent Dashboard (`dashboards/parent/Parent.tsx`)**:

     * حالة حضور الأبناء اليوم (Present/Absent).
     * موقع الحافلة الحالي في الوقت الفعلي (Mapbox).
     * إشعارات حديثة (Attendance Updates, Bus Delay, Emergency).
     * جدول الاشتراكات والمدفوعات الخاصة بالطالب.
   * **Student Dashboard (`dashboards/student/Student.tsx`)**:

     * جدول الحضور الشخصي (هفتلي/شهري).
     * الإشعارات الشخصية (مثلاً دعوتهم لتحديث الملف).
     * حالة الدفع والاشتراك للفصل الحالي.
   * **Traffic Supervisor Dashboard (`dashboards/traffic-supervisor/TrafficSupervisor.tsx`)**:

     * خريطة تعرض جميع الحافلات المنزلقة للمدرسة (كل Bus Marker بلون مختلف).
     * قائمة بالحافلات التي خرجت عن المسار (Geofence Alerts).
     * جدول بحالة التأخير لكل حافلة (Delayed Buses).
     * إمكانية التواصل السريع مع السائق (زر “اتصل بالسائق” أو إرسال رسالة داخلية).
2. **تطبيق Realtime Stats**

   * إنشاء قناة Supabase Realtime لكل Tenant:

     * `supabase.from('buses:tenant_id=eq.<tenant_id>').on('UPDATE', …)`.
     * `supabase.from('attendance:tenant_id=eq.<tenant_id>').on('INSERT', …)`.
     * `supabase.from('notifications:tenant_id=eq.<tenant_id>').on('INSERT', …)`.
   * استخدام Hook مركزي `useRealtime(channel, callback)` للاشتراك والتحديث الآني للبيانات.
   * ربط الـ Charts (Recharts) بالبيانات المباشرة، وفي حال عدم توفر Realtime، استخدام Polling خفيف (مثلاً كل 30 ثانية).
3. **التصميم والتنسيق**

   * استخدام نظام الألوان:

     * العناوين والخطوط الكبيرة بالعربي والإنجليزي بحجم متناسق (العناوين: `text-xl font-bold`, النصوص: `text-base`).
     * اللوح الرئيسية (Cards) مستديرة الزوايا (`rounded-2xl`) وظلال ناعمة (`shadow-md`).
     * استخدام ألوان Primary للأزرار الرئيسية، Secondary للأزرار الثانوية، Accent للأرقام الإيجابية، Warning للإنذارات، Error للأخطاء.
   * دعم RTL: عند اختيار اللغة العربية، يجب عكس اتجاه الصفحة تلقائيًا (`dir="rtl"`) وتبديل المواقع النسبية للعناصر (مثلاً: Sidebar على اليمين).
   * دمج Framer Motion للانتقالات بين الأقسام (مثل Fade In للعناصر عند التحميل).
4. **توثيق وتصميم**

   * **docs/details/dashboards.md**: شرح تصميم كل Dashboard، مكونات Recharts المستخدمة (Bar, Line, Pie إذا لزم الأمر)، أمثلة API Requests لجلب البيانات.
   * **docs/changelog/dashboards.md**: سجل إنشاء ملفات الـ Dashboard، إضافة Channels للـ Realtime.
5. **اختبارات**

   * اختبار تحديث البيانات مباشرة (مثلاً: إضافة سجل حضور جديد والتأكد من تحديث Chart في لوحة School Admin).
   * اختبار عرض الخريطة وتحديث موقع الحافلة في لوحة Driver وParent.
   * اختبار تبديل الاتجاه RTL عند تغيير اللغة.

### الوثائق المطلوبة

* **docs/changelog/dashboards.md**
* **docs/details/dashboards.md**

---

## المرحلة 15: نظام التقارير والإحصائيات (Reports & Analytics)

### الهدف الوظيفي

* توفير واجهة لإنشاء تقارير الحضور اليومية، الأسبوعية، الشهرية، وتقارير أداء السائقين، واستخدام الحافلات.
* إمكانية تصدير التقارير بصيغ PDF وExcel.

### المهام

1. **قاعدة البيانات**

   * إنشاء View أو Materialized View لتجميع بيانات الحضور حسب اليوم/الأسبوع/الشهر لأداء أسرع.
   * إنشاء View لتجميع تقييمات السائقين (Creative: حساب متوسط التقييم لكل سائق).
   * إنشاء View لقياس استخدام الحافلات (عدد الرحلات لكل حافلة في الفترة).
2. **الواجهة الخلفية**

   * Endpoints لتوليد التقارير:

     * `GET /api/reports/attendance?type=daily|weekly|monthly&tenant_id=...` => يعيد JSON يُظهر الأرقام المطلوبة (مثل `{ date: '2025-06-01', present: 120, absent: 10 }`).
     * `GET /api/reports/drivers-performance?tenant_id=...&period=monthly` => يعيد `{ driver_id, avg_rating, total_rides }`.
     * `GET /api/reports/buses-usage?tenant_id=...&period=weekly` => يعيد `{ bus_id, total_trips, total_distance }`.
   * دمج مكتبة توليد PDF في الباك (مثل: `pdfkit` أو `jspdf` في Supabase Functions) لإنشاء تقرير PDF في السيرفر:

     * عند استدعاء `GET /api/reports/attendance/export/pdf?type=daily`, توليد ملف PDF مخزن في Storage وترجيع رابط التنزيل.
   * إضافة Endpoints لتصدير Excel:

     * استخدام مكتبة `xlsx` أو `exceljs` لإنشاء ملف XLSX وحفظه في Storage.
   * إضافة فلترة بالمدرسة والقسم (Grade) إذا لزم الأمر.
3. **الواجهة الأمامية**

   * **صفحة AttendanceReport.tsx**:

     * اختيار نوع التقرير (Daily, Weekly, Monthly) عبر Select.
     * زر “عرض التقرير” يجلب البيانات ويعرضها في جدول وجداول (Bar Chart).
     * زر “تصدير PDF” و“تصدير Excel” يستدعي Endpoints ويعرض رابط التحميل المستلم.
   * **صفحة PerformanceReport.tsx**:

     * قائمة بالسائقين مع متوسط التقييم وعدد الرحلات.
     * رسم بياني (Bar Chart) يُظهر أعلى 5 سائقين تقييمًا.
     * إمكانية اختيار فترة زمنية مخصصة (من والتالى).
   * **صفحة BusesUsageReport.tsx**:

     * جدول يعرض: رقم الحافلة، عدد الرحلات، المسافة المقطوعة.
     * رسم بياني خطي يُظهر الاستخدام عبر الزمن.
4. **توثيق وتصميم**

   * **docs/details/reports.md**: شرح Views المكونة في قاعدة البيانات، Endpoints لتصدير ملفات PDF/Excel، هيكل التقرير (حقول).
   * **docs/changelog/reports.md**: سجل إنشاء Views والواجهات والتصدير.
5. **اختبارات**

   * اختبار توليد تقرير الحضور اليومي ومقارنة البيانات مع القيم الحقيقية في قاعدة البيانات.
   * اختبار تحميل تقرير PDF والتأكد من فتحه بشكل سليم.
   * اختبار إنشاء تقرير أداء السائق لفترة الشهر الحالي وعرضه في الـ Frontend.

### الوثائق المطلوبة

* **docs/changelog/reports.md**
* **docs/details/reports.md**

---

## المرحلة 16: نظام الصيانة (Fleet Maintenance)

### الهدف الوظيفي

* إنشاء وحدة لإدارة صيانة الأسطول: تسجيل الأعطال، جدولة الصيانة، وإرسال تذكيرات تلقائية قبل موعد الصيانة.

### المهام

1. **قاعدة البيانات**

   * جدول `maintenance`:

     * الحقول: `id`, `bus_id`, `description`, `scheduled_date`, `status` (`pending` / `in_progress` / `completed`), `tenant_id`.
   * جدول `maintenance_logs`: لتسجيل كل عملية صيانة بتاريخ التنفيذ والمهندس المسؤول، ووصف الإجراءات التي تمت.
2. **الواجهة الخلفية**

   * Endpoints الصيانة:

     * `GET /api/maintenance?tenant_id=...`.
     * `POST /api/maintenance` لإضافة طلب صيانة جديد (يتضمن وصفًا وصورة توضيحية إن وجدت).
     * `PATCH /api/maintenance/:id` لتحديث الحالة أو التاريخ (مثلاً تحويل `status` إلى `in_progress`).
     * `DELETE /api/maintenance/:id` (بعد التأكد أنه لا أثر لموظفين آخرين).
   * Supabase Function مجدولة (CRON) تعمل يوميًا في الساعة 8 صباحًا:

     * تبحث عن صيانة مجدولة خلال الـ 3 أيام القادمة.
     * ترسل تذكيرًا (Notifications) لـ School Admin والسائق المعني.
   * عند إتمام الصيانة (`status = completed`):

     * إضافة سجل في `maintenance_logs`.
     * إرسال إشعار نجاح باللون الـ Accent (أخضر) إلى School Admin.
3. **الواجهة الأمامية**

   * **صفحة MaintenanceList.tsx**:

     * جدول يعرض: رقم الحافلة، وصف العطل، التاريخ المجدول، الحالة.
     * زر “إضافة طلب صيانة” لفتح `MaintenanceForm.tsx`.
     * زر “تفاصيل” لعرض Modal يعرض معلومات الصيانة وسجل الصيانة الخاص بها.
     * زر تغيير الحالة (Dropdown يُمكّن من اختيار `in_progress` أو `completed`).
   * **مكوّن MaintenanceForm.tsx**:

     * فورم: اختيار الحافلة (Bus Selector)، حقل نص لوصف العطل، اختيار تاريخ مجدول (Date Picker)، رفع صورة توضيحية (اختياري).
     * تحقق Form: التأكد من تاريخ ≥ التاريخ الحالي.
   * **مكوّن MaintenanceLogs.tsx**:

     * يعرض السجلات السابقة للصيانة (Engineer Name, Actions Taken, Date).
   * عرض تذكيرات الصيانة في Dashboard School Admin ضمن قسم “الصيانة القادمة”.
4. **توثيق وتصميم**

   * **docs/details/maintenance.md**: شرح جدول `maintenance` و`maintenance_logs`، منطق الوظيفة المجدولة، إعداد الإشعارات التذكيرية.
   * **docs/changelog/maintenance.md**: سجل إنشاء الجداول والواجهات والإشعارات المجدولة.
5. **اختبارات**

   * اختبار إنشاء طلب صيانة جديد وتحقق من ظهور التذكير في الوقت المناسب.
   * اختبار تغيير حالة الصيانة إلى `completed` والتأكد من إضافة سجل في `maintenance_logs`.
   * اختبار عدم قبول تاريخ مجدول قبل التاريخ الحالي.

### الوثائق المطلوبة

* **docs/changelog/maintenance.md**
* **docs/details/maintenance.md**

---

## المرحلة 17: إدارة الدفع والاشتراكات (Payments & Subscriptions)

### الهدف الوظيفي

* تنفيذ نظام دفع الاشتراكات الشهري/السنوي للطلاب وربطها بالحافلات.
* دمج مع Paymob / Fawry / Stripe لدفع رسوم النقل وإنشاء فواتير PDF تلقائيًا.

### المهام

1. **قاعدة البيانات**

   * جدول `subscriptions`:

     * الحقول: `id`, `student_id`, `bus_id`, `start_date`, `end_date`, `amount`, `status` (`pending` / `active` / `expired`), `tenant_id`.
   * جدول `invoices`:

     * الحقول: `id`, `subscription_id`, `invoice_number`, `pdf_url`, `issued_at`, `due_date`, `status`, `tenant_id`.
   * جدول `payments`:

     * الحقول: `id`, `subscription_id`, `payment_method` (`paymob` / `fawry` / `stripe`), `transaction_id`, `amount`, `status` (`success` / `failed`), `paid_at`, `tenant_id`.
2. **الواجهة الخلفية**

   * إعداد Endpoints الاشتراكات:

     * `GET /api/subscriptions?student_id=...` (Student & Parent View).
     * `POST /api/subscriptions` لإنشاء اشتراك جديد (توليد `subscription_id`, `invoice_id`).
     * `PATCH /api/subscriptions/:id` لتحديث الحالة (مثل: تغيير `status` إلى `active` بعد الدفع).
   * إعداد Endpoints الفواتير:

     * `GET /api/invoices/:id` لإرجاع رابط الـ PDF.
     * `POST /api/invoices/generate/:subscription_id` لبناء فاتورة PDF باستخدام `jspdf` أو `pdfkit`:

       * تضمين تفاصيل الطالب، تفاصيل الحافلة، الفترة، المبلغ، إلخ.
       * حفظ الملف في Supabase Storage أو S3، وإرجاع الرابط.
   * إعداد Endpoints الدفع:

     * `POST /api/payments/initiate` يستقبل `{ subscription_id, payment_method }`، يقوم بقراءة بيانات الاشتراك والمبلغ ثم يوجّه المستخدم إلى واجهة الدفع المناسبة (Paymob/Fawry).
     * **Webhook Endpoints** لاستقبال إخطار الدفع (Paymob/Fawry) وتحديث حالة الدفعة:

       * عند الدفع الناجح: إنشاء سجل في جدول `payments` وتحديث `subscription.status = ‘active’`.
       * عند الدفع الفاشل: إنشاء سجل مع `status = failed`.
   * إعداد الوظيفة المجدولة (CRON) لتذكير الطلاب المنتهية اشتراكاتهم قبل 7 أيام: إرسال إشعار (type = `subscription_reminder`) للولي الأمر والطالب.
3. **الواجهة الأمامية**

   * **صفحة SubscriptionsList.tsx** (Parent & Student):

     * جدول يعرض: الفترة (Start-End)، حالة الاشتراك (Active/Pending/Expired)، المبلغ، زر “تجديد” إذا كان منتهيًا أو قريب الانتهاء.
     * زر “تفاصيل الفاتورة” يعرض Modal به رابط التنزيل (PDF).
   * **صفحة SubscriptionForm.tsx** (School Admin):

     * فورم لاختيار الطالب والحافلة والفترة (شهر/سنة).
     * بعد التحديد: زر “إنشاء فاتورة ودفع” يُوجّه للـ Payment Initiation Endpoint.
   * **مكوّن InvoiceViewer.tsx**:

     * يعرض معاينة مصغرة للفاتورة PDF داخل Modal (باستخدام `react-pdf`).
   * **مكوّن PaymentStatus.tsx**:

     * يعرض حالة الدفع (نجاح أو فشل) بعد عودة المستخدم من بوابة الدفع.
4. **توثيق وتصميم**

   * **docs/details/payments-subscriptions.md**: شرح جداول `subscriptions`, `invoices`, `payments`، منطق توليد الفاتورة، إعداد Webhooks لبوابات الدفع.
   * **docs/changelog/payments-subscriptions.md**: سجل إنشاء الجداول والواجهات والوظائف المجدولة.
5. **اختبارات**

   * اختبار إنشاء اشتراك جديد وتجربة الدفع عبر بيئة Sandbox في Paymob/Fawry.
   * اختبار توليد فاتورة PDF والتأكد من صحتها (بيانات الطالب، الحافلة، الفترة، المبلغ).
   * اختبار تذكير الاشتراك قبل 7 أيام والتأكد من وصول الإشعار.

### الوثائق المطلوبة

* **docs/changelog/payments-subscriptions.md**
* **docs/details/payments-subscriptions.md**

---

## المرحلة 17.1: نظام العروض والاشتراكات (Offers & Packages)

*مُدرجة في سياق نفس المرحلة العشرينية للدفع، ولكنّها تضيف مرونة لإدارة الخطط إن رغب المستخدم في إضافة عروض خاصة.*

### الهدف الوظيفي

* إضافة وحدة لإدارة العروض والخصومات: تحديد خصم لفترة معينة أو باقات اشتراك خاصة بالتسجيل المبكر.

### المهام

1. **قاعدة البيانات**

   * جدول `offers`:

     * الحقول: `id`, `name_ar`, `name_en`, `description_ar`, `description_en`, `discount_type` (`percentage` / `fixed_amount`), `discount_value`, `start_date`, `end_date`, `applicable_to` (`all_tenants` / `specific_tenant`), `tenant_id` (nullable).
   * جدول وسيط `subscription_offers`: لربط العروض بالاشتراكات (إن استدعى الأمر).
2. **الواجهة الخلفية**

   * Endpoints إدارة العروض:

     * `GET /api/offers?tenant_id=...` (School Admin sees Offer خاص بمدرسته أو العروض العامة).
     * `POST /api/offers` لإنشاء عرض جديد (يمكن أن يكون عام لجميع المدارس أو مخصّص لمدرسة معينة).
     * `PATCH /api/offers/:id` لتعديل الخصم وتواريخ الانتهاء.
     * `DELETE /api/offers/:id`.
   * عند Initiate Subscription:

     * في `POST /api/payments/initiate`, إضافة منطق حساب الخصم إذا كانت هناك عروض صالحة (تحقق من تاريخ الاشتراك ونوع العرض).
3. **الواجهة الأمامية**

   * **صفحة OffersList.tsx** (School Admin):

     * جدول يعرض: اسم العرض، نوع الخصم، قيمة الخصم، تاريخ البداية والنهاية، الحالة (Active/Expired).
     * زر “إضافة عرض” لفتح Modal مع فورم إنشاء عرض جديد.
   * **مكوّن OfferForm.tsx**:

     * فورم: الاسم بالعربي/إنجليزي، الوصف، اختيار نوع الخصم (Percentage/Fixed)، قيمة الخصم، اختيار تاريخ البداية والنهاية.
     * حقل اختيار: “يطبق على: كل المدارس” (Checkbox) أو “مدرسة محددة” (Dropdown لاختيار المدرسة).
   * **On SubscriptionForm.tsx**:

     * إذا كان هناك عرض صالح، إظهار تفاصيل الخصم أسفل حقل المبلغ (مثلاً: “يطبق خصم 10% حتى تاريخ ...”).
     * زر حساب تلقائي للمبلغ بعد الخصم.
4. **توثيق وتصميم**

   * **docs/details/offers.md**: شرح جدول `offers`، كيفية حساب الخصم أثناء إنشاء الاشتراك، أمثلة Payload عند وجود خصم.
   * **docs/changelog/offers.md**: سجل إنشاء جدول العروض وإضافة الواجهات.
5. **اختبارات**

   * اختبار إنشاء عرض بنسبة مئوية والتحقق من تطبيقه على اشتراك يتم إنشاؤه خلال الفترة المحددة.
   * اختبار عرض عرض العرض المُنشأ في واجهة SubscriptionForm مع تعديل المبلغ تلقائيًا.

### الوثائق المطلوبة

* **docs/changelog/offers.md**
* **docs/details/offers.md**

---

## المرحلة 18: تحسين تجربة المستخدم (UI/UX Enhancements & Mobile Responsiveness)

### الهدف الوظيفي

* ضبط واجهات المستخدم لتكون متوافقة مع جميع الشاشات (Desktop, Tablet, Mobile) باستخدام Responsive Design.
* إضافة تأثيرات سلسة (transitions) وحركية (animations) لتحسين الانسيابية.
* ضمان دعم كامل للغة العربية (RTL) والإنجليزية (LTR).

### المهام

1. **الواجهة الأمامية**

   * في `tailwind.config.js` تأكد من ضبط `screens` لتغطية `sm, md, lg, xl` ثم اختبار المكونات على هذه الأحجام.
   * استخدام مكونات Radix UI مع Props الخاصة بالـ responsive (مثلاً: `DialogContent` بعرض مختلف لكل حجم شاشة).
   * إضافة Framer Motion:

     ```bash
     npm install framer-motion
     ```
   * في الـ Pages الرئيسية (مثل Dashboard)، لف العناصر الأساسية بـ`<motion.div>` مع تأثير `fadeIn` أو `slideUp` عند تحميل الصفحة.
   * ضبط القوائم (Sidebars, Dropdowns) لتظهر بشكل متحرك (`transition-all duration-300`).
   * تحسين تجربة Dark Mode:

     * أضف قصداً لتغيير الألوان التكميلية عند التبديل (مثلاً: Background: `bg-gray-900` في الوضع الداكن).
     * تأكد من اتساق الألوان (النصوص، الحدود، الخلفيات).
   * إضافة مدخلات ذكية (Smart Inputs):

     * في صفحة تسجيل الطالب أو إضافة الحافلة، التحقق الفوري من صحة الحقول (مثل: رقم الهاتف بصيغة مصرية صحيحة).
     * عرض رسائل خطأ/نجاح على شكل Toast أو Inline.
2. **اختبار تجاوب الواجهة**

   * استخدام أدوات مطوري المتصفح لمحاكاة الهواتف والأجهزة اللوحية:

     * التأكد من أن القوائم الجانبية (Sidebars) تختفي في الشاشات الصغيرة وتصبح قائمة Hamburger تظهر أعلى الصفحة.
     * التأكد من أن الجداول في صفحات Reports وLists تتحول إلى Scroll أفقي في الشاشات الضيقة.
   * اختبار أدوات الـ Keyboard Navigation ووصولية الألوان (Contrast) لضمان تجربة أفضل لذوي الاحتياجات الخاصة.
3. **توثيق وتصميم**

   * **docs/details/ui-ux.md**:

     * شرح مقاسات الشاشات المدعومة (breakpoints) وكيفية التعامل معها في Tailwind.
     * قائمة بالمكونات التي أضيفت إليها Framer Motion وتأثيراتها.
     * أمثلة على حالات RTL وLTR وكيف تم ضبط الصفحات لتبديل الاتجاه.
   * **docs/changelog/ui-ux.md**: سجل إضافات الـ responsive، الـ animations، والتحسينات العامة للواجهات.
4. **اختبارات**

   * اختبارات يدوية لمحاكاة الأجهزة (Desktop, iPad, iPhone) والتأكد من استجابة التصميم.
   * اختبار تغييرات الوضع الداكن/الفاتح والتأكد من اتساق الألوان والنصوص.
   * اختبار الوصولية (Accessibility Audit) بواسطة Lighthouse أو axe.

### الوثائق المطلوبة

* **docs/changelog/ui-ux.md**
* **docs/details/ui-ux.md**

---

## المرحلة 19: دعم متعدد اللغات كامل (Internationalization – i18n)

### الهدف الوظيفي

* ضمان دعم كامل للغة العربية والإنجليزية، مع تبديل تلقائي لاتجاه النص (RTL/LTR) بناءً على اللغة المختارة.
* إعداد إطار ترجمة موحد (Key-based i18n) لتفادي التكرار وضمان الترجمة السليمة.

### المهام

1. **الواجهة الأمامية**

   * **تهيئة i18next** في ملف `src/i18n.ts`:

     * إنشاء ملفات الترجمة:

       ```
       src/locales/
       ├── en/
       │   └── common.json
       └── ar/
           └── common.json
       ```
     * في كل ملف JSON: إسناد نصوص الواجهة إلى مفاتيح موحدة (مثلاً: `"login.title": "Login", "login.title": "تسجيل الدخول"`).
   * إضافة `LanguageSwitcher.tsx`:

     * زر لتبديل اللغة محفوظ في الـ Context.
     * عند التبديل:

       ```tsx
       i18n.changeLanguage(lang);
       document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
       document.documentElement.setAttribute('lang', lang);
       ```
   * تحديث جميع النصوص الثابتة (مثل: عناوين الصفحات، تسميات الحقول، رسائل الأخطاء) لاستخدام Hook `t('key')`.
   * اختبار اتجاه الصفحة RTL/LTR:

     * في الحاوية الرئيسية (`<App />`)، ضفّ `<div className={i18n.language === 'ar' ? 'rtl' : 'ltr'}>` لضبط خصائص Tailwind (مثل: `text-right` أو `text-left`).
   * ضبط مكونات Radix UI وRecharts لعرض النصوص بشكل صحيح عند RTL (مثلاً: في الرسوم البيانية، عكس اتجاه المحور X).
2. **الواجهة الخلفية (Error Messages, Validation)**

   * تجهيز ملفات الترجمة للواجهات الخلفية (Validation Messages) إن كان هناك Supabase Functions تعيد أخطاء باللغة الانجليزية أو العربية.
   * تأكد من أن الرسائل المُعادة من Backend تحتوي على مفاتيح يمكن ترجمتها في الـ Frontend.
3. **توثيق وتصميم**

   * **docs/details/i18n.md**: شرح بنية ملفات الترجمة (JSON)، طريقة استخدام `t('key')` في المكونات، كيفية ضبط الـ `dir` و `lang` في وثيقة HTML.
   * **docs/changelog/i18n.md**: سجل إنشاء ملفات الترجمة وإضافة LanguageSwitcher.
4. **اختبارات**

   * اختبار تبديل اللغة إلى العربي والتأكد من RTL لكل الصفحات (Sidebar يمين، القوائم من اليمين إلى اليسار).
   * اختبار وجود جميع المفاتيح في ملفات الترجمة (unit test للتأكد من عدم وجود Keys مفقودة).
   * اختبار ضبط خطوط Cairo في النص العربي (التأكد من تطبيق الخط عبر CSS).

### الوثائق المطلوبة

* **docs/changelog/i18n.md**
* **docs/details/i18n.md**

---

## المرحلة 20: النظام المعياري والوحدات الإضافية (Plug-in Modules)

### الهدف الوظيفي

* تقسيم النظام إلى وحدات مستقلة (Plug-ins) يمكن تفعيلها/تعطيلها لكل مدرسة (Tenant).
* جعل كل وحدة تعمل بصورة مستقلة قدر الإمكان وتُسجل الأحداث في سجل مركزي.

### المهام

1. **تصميم هيكل `apps/`**

   * لكل وحدة (Module) مجلد مستقل داخل `apps/`:

     ```
     apps/
     ├── auth/             # مُكتمل (مرحلة 2 و3)
     ├── users/            # مُكتمل (مرحلة 6)
     ├── students/         # مُكتمل (مرحلة 7)
     ├── parents/          # مُكتمل (مرحلة 8)
     ├── drivers/          # مُكتمل (مرحلة 9)
     ├── buses/            # مُكتمل (مرحلة 9)
     ├── routes/           # مُكتمل (مرحلة 10)
     ├── attendance/       # مُكتمل (مرحلة 12)
     ├── gps-tracking/     # مُكتمل (مرحلة 11)
     ├── notifications/    # مُكتمل (مرحلة 13)
     ├── maintenance/      # مُكتمل (مرحلة 16)
     ├── payments/         # مُكتمل (مرحلة 17)
     ├── reports/          # مُكتمل (مرحلة 15)
     ├── offers/           # مُكتمل (المرحلة 17.1)
     ├── analytics/        # قيد التطوير (سيتم تغطيته في المرحلة التالية)  
     └── gamification/     # لم يتم البدء به بعد (مرحلة لاحقة)
     ```
   * في كل مجلد وحدة:

     ```
     apps/<module>/
     ├── domain/           # نماذج البيانات (Interfaces, Types)
     ├── application/      # منطق العمل (Services, Use Cases)
     ├── infrastructure/   # التفاعل مع DB أو الخدمات الخارجية
     ├── ui/               # صفحات ومكونات خاصة بالوحدة
     └── tests/            # اختبارات الوحدة
     ```
2. **آلية تفعيل/تعطيل الوحدة لكل Tenant**

   * في جدول `tenants`, إضافة عمود `enabled_modules` (Array of Strings) يحوي أسماء الوحدات المُفعلة.
   * في Backend Middleware أو Function ليمنع تنفيذ Endpoints الخاصة بالوحدات غير المفعلة:

     * مثلاً: عند استدعاء `/api/attendance/...`، تحقق أولاً من أن `attendance` موجودة في `enabled_modules` للـ tenant.
3. **تعديل الواجهة الأمامية لعرض/إخفاء القوائم**

   * في Sidebar الرئيسي (`Layout/Sidebar.tsx`):

     * جلب قائمة `enabled_modules` من Endpoint: `GET /api/tenants/:id/modules`.
     * بناء القائمة الجانبية ديناميكيًا بحيث يظهر رابط الوحدة فقط إذا كانت مفعّلة.
   * عند محاولة الدخول يدويًا إلى مسار وحدة غير مفعّلة (`/attendance`)، إعادة التوجيه إلى صفحة خطأ أو Dashboard الرئيسي.
4. **توثيق وتصميم**

   * **docs/details/modules.md**: شرح آلية تفعيل الوحدات، بنية مجلد `apps/` وأدوار كل طبقة (domain, application, infrastructure, ui).
   * **docs/changelog/modules.md**: سجل هيكلة الوحدات وتنفيذ الميزة.
5. **اختبارات**

   * اختبار إخفاء رابط وحدة غير مفعّلة في الـ Sidebar.
   * محاولة الوصول يدويًا إلى مسار وحدة غير مفعّلة والتأكد من إعادة التوجيه.
   * اختبار تفعيل وحدة جديدة وإظهارها في الـ Sidebar فورًا.

### الوثائق المطلوبة

* **docs/changelog/modules.md**
* **docs/details/modules.md**

---

## المرحلة 21: إمكانيات ذكية متقدمة (Advanced Features – AI Route Optimization & Biometric Verification)

### الهدف الوظيفي

* إضافة ميزة اختيار أفضل المسارات يوميًا باستخدام خوارزمية ذكاء اصطناعي بسيطة (AI).
* دمج التحقق البيومتري (Biometric Verification) باستخدام بصمة وجه/بصمة إصبع للطالب أو السائق عند تسجيل حضورهم أو بدء الرحلة.

### المهام

1. **AI Route Optimization**

   1. **قاعدة البيانات**

      * جدول `route_history` لحفظ أداء المسار اليومي: عدد الطلاب، زمن الرحلة الفعلي، التأخيرات.
   2. **الواجهة الخلفية**

      * Supabase Function مجدولة (CRON) تعمل قبل بدء اليوم الدراسي (مثلاً الساعة 6 صباحًا):

        * تجمع بيانات `route_history` للفترة الأخيرة.
        * تمررها عبر خوارزمية بسيطة (Greedy أو Nearest Neighbor Algorithm) لاختيار ترتيب أفضل لمحطات كل مسار بناءً على كثافة الطلاب والمسافات.
        * تحفظ النتائج في جدول `optimized_routes`:

          * الحقول: `route_id`, `date`, `optimized_stops_order` (Array of Stop IDs), `estimated_time`.
      * Endpoint: `GET /api/optimized-routes?date=YYYY-MM-DD&tenant_id=...` لعرض النتيجة.
   3. **الواجهة الأمامية**

      * **صفحة OptimizedRoutes.tsx**:

        * يعرض للمشرف أو School Admin قائمة بالمسارات مع أفضل ترتيب للنقاط ليوم معين.
        * عرض خريطة تظهر المسار المحسن (Polyline) مع الإشارة إلى ترتيب النقاط.
   4. **توثيق وتصميم**

      * **docs/details/ai-optimization.md**: شرح الخوارزمية المستخدمة، بنية جدول `optimized_routes`، مثال على Payload.
      * **docs/changelog/ai-optimization.md**: سجل إنشاء الوظيفة المجدولة والواجهات.

2. **Biometric Verification**

   1. **قاعدة البيانات**

      * جدول `biometric_data`:

        * الحقول: `id`, `user_id`, `face_template` (Binary أو String)، `fingerprint_template` (إن دعت الحاجة)، `tenant_id`.
   2. **الواجهة الخلفية**

      * Supabase Function أو خدمة طرف ثالث (مثل AWS Rekognition) لحفظ ومقارنة الـ biometric templates.
      * Endpoint: `POST /api/biometric/register` لاستقبال صورة الوجه أو البصمة أثناء إنشاء حساب السائق/الطالب.
      * Endpoint: `POST /api/biometric/verify` يستقبل صورة/بصمة حية من التطبيق المحمول/الويب ويقارنه بالقالب المخزن:

        * في حال التطابق بنسبة ≥ 90%، يرد بنجاح ويتيح للمستخدم تسجيل الدخول أو بدء الرحلة أو تسجيل حضور الطالب.
   3. **الواجهة الأمامية**

      * **مكوّن FaceCapture.tsx**:

        * يستخدم كاميرا الجهاز لالتقاط صورة الوجه، يعرض إطارًا داخليًا (Face Bounding Box) للتوجيه.
        * بعد الالتقاط، يرسل الصورة إلى Endpoint `verify`.
      * **صفحة BiometricSetup.tsx**:

        * يسمح لـ School Admin بتسجيل بصمة وجه للطالب/السائق من خلال كاميرا الويب.
        * يظهر تعليمات بالعربية والإنجليزية لكيفية التقاط صورة واضحة.
   4. **توثيق وتصميم**

      * **docs/details/biometric.md**: شرح طريقة توليد ومقارنة الـ Templates، الإعدادات الأمنية (تشفير البيانات)، متطلبات الكاميرا.
      * **docs/changelog/biometric.md**: سجل إنشاء جدول `biometric_data` والواجهات.
   5. **اختبارات**

      * اختبار تسحيل وجه الطالب في الواجهة ومطابقتها في الـ Backend (استخدم صورة اختبارية موحدة).
      * اختبار فشل التحقق عندما تكون الصورة غير واضحة أو مختلفة.

### الوثائق المطلوبة

* **docs/changelog/ai-optimization.md**
* **docs/details/ai-optimization.md**
* **docs/changelog/biometric.md**
* **docs/details/biometric.md**

---

## المرحلة 22: وضع الطوارئ (Emergency Mode & Geo-fence Alerts)

### الهدف الوظيفي

* تنفيذ زر طوارئ يرسل إشعارًا فوريًا للمسؤول وولي الأمر مع الموقع الحالي للحافلة/السائق/الطالب.
* تحسين إنذارات الـ Geo-fencing (تم البدء بها في المرحلة 11) بحيث تتضمن صورًا أو مقاطع صوتية في حالة الطوارئ.

### المهام

1. **قاعدة البيانات**

   * جدول `emergency_logs`:

     * الحقول: `id`, `triggered_by` (user\_id), `bus_id`, `student_id` (nullable), `location GEOGRAPHY(Point)`, `timestamp`, `details` (Text JSON), `tenant_id`.
   * توسيع جدول `notifications` لإضافة نوع جديد “emergency\_alert” مع حقل `severity` (مثلاً: low/medium/high).
2. **الواجهة الخلفية**

   * Endpoint: `POST /api/emergency/trigger` يستقبل `{ triggered_by, bus_id?, student_id?, location }`.

     * حفظ سجل في `emergency_logs`.
     * إنشاء إشعار (type = “emergency\_alert”) لجميع أصحاب الأدوار التالية:

       * School Admin، System Admin، Parents of related student(s)، Traffic Supervisor.
     * إذا كانت الحالة حرجة (بناءً على بيانات `details`)، إرسال SMS عاجل للأدوار السابقة.
   * توسيع منطق Geo-fencing لإرسال إشعار بـ “emergency\_alert” إذا خرج الطالب عن المنطقة المصرح بها (تم دمج/ابتدأ في المرحلة 11).
3. **الواجهة الأمامية**

   * **صفحة LiveTracking.tsx** و**Driver Dashboard**:

     * إضافة زر “طوارئ” ثابت في الأعلى (Floating Button) بحجم مناسب (32px × 32px)، بلون أحمر (#ef4444)، مع أيقونة خفيفة (Lucide: AlertCircle).
     * عند الضغط:

       1. عرض Modal يُطالب المستخدم بتأكيد الإجراء وإرفاق تفاصيل (حقل نص قصير).
       2. بعد التأكيد، التقاط الموقع الحالي تلقائيًا عبر Geolocation API.
       3. إرسال طلب إلى `POST /api/emergency/trigger`.
   * **صفحة EmergencyLogs.tsx** (School Admin وTraffic Supervisor):

     * جدول مرتب تنازليًا حسب `timestamp` يحتوي على: وقت الطوارئ، الشخص المسبب (Driver/Student)، موقع الحادث (عرض مختصر على الخريطة)، تفاصيل الطوارئ، الإجراءات المتخذة.
     * زر “عرض التفاصيل” لفتح Modal يعرض خريطة أكبر ومعلومات مفصلة (بما في ذلك روابط الصور/الصوت إن وجدت).
   * **مكوّن EmergencyNotification.tsx**:

     * عند استقبال إشعار “emergency\_alert” عبر Realtime: عرض بانر أحمر في أعلى الشاشة يحتوي على عنوان الطوارئ وزر “عرض الخريطة”.
     * عند الضغط، يعرض Modal يُبيّن موقع الحادث وتفاصيله.
4. **توثيق وتصميم**

   * **docs/details/emergency.md**: شرح جدول `emergency_logs`، Endpoint `/api/emergency/trigger`، آلية إرسال الإشعارات العاجلة، وصف بنية `details` في السجل.
   * **docs/changelog/emergency.md**: سجل إنشاء الجدول والواجهات والزر في الواجهة الأمامية.
5. **اختبارات**

   * اختبار الضغط على زر “طوارئ” وإنشاء سجل في `emergency_logs`.
   * اختبار إرسال إشعار “emergency\_alert” واستلامه في Dashboard المناسب.
   * اختبار سيناريو خروج الطالب عن المنطقة المصرح بها وإنشاء سجل طوارئ تلقائيًا.

### الوثائق المطلوبة

* **docs/changelog/emergency.md**
* **docs/details/emergency.md**

---

## المرحلة 23: تقارير تحليلية متقدمة (Advanced Analytics & BI Integration)

### الهدف الوظيفي

* توفير لوحة تحليلية (BI Dashboard) لعرض مؤشرات الأداء الرئيسية (KPIs) وتحليل الاتجاهات عبر استخدام Recharts ودمج إمكانية تحميل البيانات في أدوات تحليل خارجية (مثل Metabase أو Power BI).

### المهام

1. **قاعدة البيانات**

   * إنشاء Views إضافية لـ KPIs:

     * `vw_student_attendance_rates` (نسبة الحضور لكل صف/قسم شهريًا).
     * `vw_bus_efficiency` (عدد الرحلات الفعلية مقابل المخطط لها).
     * `vw_driver_performance` (مجمع تقييمات السائقين + حالات التأخير).
   * إعداد جدول `analytics_exports` لتتبع طلبات التصدير (user\_id, report\_type, filter\_params, export\_url, created\_at).
2. **الواجهة الخلفية**

   * Endpoints Analytics:

     * `GET /api/analytics/kpi-overview?tenant_id=...` => ترجع قيم KPIs الرئيسية (مثل `{ attendance_rate: 0.95, avg_bus_utilization: 0.80, avg_driver_rating: 4.5 }`).
     * `GET /api/analytics/trends?metric=attendance&period=monthly&tenant_id=...` => ترجع بيانات طريقية (Time Series) لكل شهر.
     * `POST /api/analytics/export` => ينشئ ملف CSV جاهز للتصدير استنادًا إلى الفلاتر المحددة (مثلاً: جلب بيانات الطلاب وحضورهم للسنوات الثلاث الماضية)، ثم يحفظ الرابط في `analytics_exports`.
3. **الواجهة الأمامية**

   * **صفحة AnalyticsDashboard.tsx**:

     * علبة عرض KPIs رئيسية في الأعلى (Cards) تُظهر الأرقام المجملة.
     * ثلاثة Charts ضمن Grid:

       1. Line Chart لعرض نسبة الحضور عبر الأشهر (Data من `/api/analytics/trends?metric=attendance`).
       2. Bar Chart لـ Bus Utilization (Data من `/api/analytics/trends?metric=bus_utilization`).
       3. Pie Chart لتوزيع تقييمات السائقين (Percentage في شرائح).
     * زر “تصدير البيانات” أسفل الصفحة يفتح Modal لاختيار نوع التصدير (CSV, JSON)، وحفظ ملف في Storage، ثم عرض الرابط للمستخدم.
   * استخدام Recharts فقط (بدون تحديد ألوان يدوية):

     ```tsx
     <LineChart data={data}>
       <XAxis dataKey="month" />
       <YAxis />
       <Tooltip />
       <Line type="monotone" dataKey="attendance_rate" stroke="#3b82f6" />
     </LineChart>
     ```

     * لاحظ أن `stroke="#3b82f6"` هو اللون Primary ولا يتناقض مع القواعد (إذن مسموح لذلك اللون الأساسي).
   * **مكوّن ExportModal.tsx**:

     * فورم يتيح اختيار فترة زمنية (من – إلى)، تحديد Metric، اختيار الصيغة (CSV/JSON)، وحقل “اسم الملف”.
     * عند التأكيد: إرسال طلب إلى `/api/analytics/export`.
   * **مكوّن ExportList.tsx** (داخل AnalyticsDashboard):

     * جدول يعرض طلبات التصدير السابقة (اسم الملف، نوع التقرير، تاريخ الإنشاء، رابط التنزيل).
4. **توثيق وتصميم**

   * **docs/details/analytics.md**: شرح Views (`vw_student_attendance_rates`, `vw_bus_efficiency`, `vw_driver_performance`)، Endpoints التحليلات، هيكل البيانات المرجعة.
   * **docs/changelog/analytics.md**: سجل إنشاء Views والواجهات والوظائف الخاصة بالتصدير.
5. **اختبارات**

   * اختبار استدعاء `/api/analytics/kpi-overview` والتأكد من دقة القيم بناءً على بيانات الاختبار.
   * اختبار رسم الـ Line Chart وتحديثه عند تغيير الفلاتر (مثال: تبديل الفترة الشهرية إلى فصلية).
   * اختبار إنشاء طلب تصدير البيانات وتنزيل الملف الناتج.

### الوثائق المطلوبة

* **docs/changelog/analytics.md**
* **docs/details/analytics.md**

---

## المرحلة 24: إضافة نظام التقييم والـ Gamification

### الهدف الوظيفي

* إعداد وحدة تتيح تقييم السائقين من قبل أولياء الأمور بعد كل رحلة.
* إضافة نظام نقاط للطلاب (Gamification) لتحفيز السلوك الإيجابي والحضور المنتظم.

### المهام

1. **قاعدة البيانات**

   * جدول `driver_ratings`:

     * الحقول: `id`, `driver_id`, `parent_id`, `student_id`, `rating` (1–5), `comment_ar`, `comment_en`, `timestamp`, `tenant_id`.
   * جدول `student_points`:

     * الحقول: `id`, `student_id`, `date`, `points_earned`, `reason` (مثلاً: حضور مبكر، سلوك إيجابي)، `tenant_id`.
   * جدول `points_redemptions` (إن كان هناك آلية لاستبدال النقاط بمكافآت):

     * الحقول: `id`, `student_id`, `points_spent`, `reward_description`, `date`, `tenant_id`.
2. **الواجهة الخلفية**

   * Endpoints تقييم السائقين:

     * `POST /api/driver-ratings` لإرسال تقييم جديد (يتطلب `driver_id`, `parent_id`, `student_id`, `rating`, `comment`).
     * `GET /api/driver-ratings?driver_id=...&period=monthly` لإحصاء متوسط التقييم وعدد التقييمات.
   * Endpoints نظام النقاط:

     * `POST /api/student-points` لإنشاء سجل نقاط جديد (مثلاً: +5 نقاط لحضور مبكر، يتم تنفيذه تلقائيًا بواسطة Function مجدولة أو Backend عند حدث الحضور).
     * `GET /api/student-points?student_id=...` لإظهار رصيد النقاط الحالي وسجل الكسب / الصرف.
     * `POST /api/points-redemptions` لاستبدال النقاط (يتحقق من وجود رصيد كافٍ).
   * Supabase Function يتم استدعاؤها بعد تسجيل حضور مبكر (قبل الموعد بـ10 دقائق):

     * تضيف `points_earned = 5` في جدول `student_points`.
     * إذا كان الحضور متأخرًا، تضيف `points_earned = 0` وترسل إشعار (No Points).
   * تحديث جدول `buses` أو جدول منفصل لتتبع تأثير التقييمات (مثلاً: `avg_rating` في حقل محدث بانتظام).
3. **الواجهة الأمامية**

   * **صفحة DriverRatingForm.tsx** (Parent):

     * فورم: اختيار تقييم (Star Rating 1–5 يعرضه كمكونات Lucide أو أي مكتبة Ratings خفيفة)، حقل نص للتعليق (Ar/En).
     * زر “إرسال التقييم” يعرض تأكيدًا عند النجاح.
   * **صفحة DriverPerformance.tsx** (School Admin):

     * جدول يعرض: السائق، متوسط التقييم الشهري، عدد التقييمات، إمكانية الضغط على السائق لعرض كل التقييمات الرسائلية.
   * **صفحة StudentGamification.tsx** (Student & Parent):

     * يعرض رصيد النقاط الحالي (مع استعلام `/api/student-points?student_id=...`).
     * جدول يظهر سجل النقاط المكتسبة والتواريخ (الحضور المبكر، سلوك إيجابي، إلخ).
     * زر “استبدال النقاط” يفتح Modal للاختيار من قائمة المكافآت المتاحة (مثلاً: قسيمة خصم بسيطة، هدايا رمزية).
   * **صفحة RewardsList.tsx** (School Admin):

     * CRUD للعروض التي يُمكن للطالب استبدال النقاط بها (Reward Description, Points Required).
     * ربط المكافآت بحقل `rewards` في جدول منفصل إن دعت الحاجة.
4. **توثيق وتصميم**

   * **docs/details/gamification.md**: شرح جداول `driver_ratings`, `student_points`, `points_redemptions`, منطق إضافة النقاط، آلية الحساب.
   * **docs/changelog/gamification.md**: سجل إنشاء الجداول والواجهات والوظائف المجدولة.
5. **اختبارات**

   * اختبار إرسال تقييم سائق جديد وحساب المتوسط الصحيح.
   * اختبار إضافة نقاط طالب عند الحضور المبكر باستخدام Function المجدولة (محاكاة التأكد من إضافة النقاط).
   * اختبار استبدال النقاط والتأكد من نقص الرصيد وحفظ سجل الاستبدال.

### الوثائق المطلوبة

* **docs/changelog/gamification.md**
* **docs/details/gamification.md**

---

## المرحلة 25: دعم المدارس المتعددة في نفس الحساب (Group Tenants) & إدارة ولي أمر لأكثر من طالب

### الهدف الوظيفي

* تمكين شبكة مدارس كبيرة (Multi-Schools) من إدارة عدة مدارس تحت حساب واحد (Group Tenant).
* السماح لولي الأمر بمتابعة أكثر من طالب عبر مدارس مختلفة (إن كان الوالد لديه أبناؤه في مدرستين مختلفتين ضمن نفس الـ Group Tenant).

### المهام

1. **قاعدة البيانات**

   * جدول `group_tenants`:

     * الحقول: `id`, `group_name`, `admin_user_id` (User ID للمدير الأعلى), `created_at`.
   * إضافة حقل `group_tenant_id` في جدول `tenants` (Schools) ليكون لكل مدرسة رابط إلى المجموعة إذا كانت تنتمي لمجموعة.
   * إضافة حقل `group_tenant_id` في جدول `users` إن لزم الأمر (مثل: إذا كان للآباء حق متابعة طلاب من مدارس مختلفة ضمن نفس المجموعة).
2. **الواجهة الخلفية**

   * Endpoints Group Tenants:

     * `GET /api/group-tenants` (System Admin فقط).
     * `POST /api/group-tenants` لإنشاء مجموعة جديدة وربط مدير المجموعة (admin\_user\_id).
     * `PATCH /api/group-tenants/:id` لتعديل اسم المجموعة أو تغيير المدرب المسؤول.
     * `DELETE /api/group-tenants/:id` (مع ضمان عدم وجود مدارس مرتبطة).
   * عند إنشاء مدرسة جديدة بإمكان System Admin أو Group Admin:

     * إذا كان ضمن مجموعة، يتم تمرير `group_tenant_id`.
   * تحديث منطق الحصول على بيانات المستخدم (Parent) بحيث يبحث في جميع المدارس المرتبطة بنفس الـ `group_tenant_id` لاسترجاع الطلاب.
3. **الواجهة الأمامية**

   * **صفحة GroupTenantsList.tsx** (System Admin):

     * جدول يعرض: اسم المجموعة، مدير المجموعة (اسم المستخدم)، عدد المدارس المرتبطة.
     * زر “إنشاء مجموعة جديدة” يفتح Modal مع فورم: اسم المجموعة، اختيار مدير المجموعة (من مستخدمي النظام).
     * زر “عرض التفاصيل” لكل مجموعة لعرض قائمة المدارس المرتبطة وإمكان إضافة/حذف مدارس من المجموعة.
   * **صفحة ParentDashboard.tsx** (Parent):

     * إذا كان Parent مرتبطًا بأكثر من طالب في مدارس مختلفة ضمن نفس المجموعة:

       * عرض قائمة منسدلة بـ “اختر اسم الطالب” أو عرض Tabs لكل طالب يحتوي كل Tab بيانات الحضور والإشعارات الخاصة بهذا الطالب.
   * تحديث Hook `useCurrentTenant()` في الواجهة الأمامية ليأخذ بعين الاعتبار `group_tenant_id` عند الحاجة (مثلاً، في حال تعدد المدارس).
4. **توثيق وتصميم**

   * **docs/details/group-tenants.md**: شرح جداول `group_tenants` وضم المدارس للمجموعة، تحديث جداول `tenants` و`users`.
   * **docs/changelog/group-tenants.md**: سجل إنشاء جدول المجموعة وإضافة الواجهات.
5. **اختبارات**

   * اختبار إنشاء مجموعة وربط عدة مدارس بها.
   * اختبار Parent لديه طالبين في مدرستين مختلفتين ضمن نفس المجموعة، والتبديل بين بيانات الطالبين في الـ Dashboard.
   * اختبار وصول Group Admin لإدارة المدارس فقط داخل مجموعته.

### الوثائق المطلوبة

* **docs/changelog/group-tenants.md**
* **docs/details/group-tenants.md**

---

## المرحلة 26: سجل المراجعة الكامل (Audit Log) & إدارة الجلسات

### الهدف الوظيفي

* تعقب كل العمليات الحيوية في النظام (CRUD على الكيانات الرئيسية، تغييرات الصلاحيات، تسجيل الدخول/الخروج) وحفظها في سجل مراجعة مفصل.
* توفير واجهة لعرض سجل الأحداث (Audit Trail) وإمكانية تصديره.
* إدارة الجلسات النشطة لكل مستخدم والسماح للأدمن بإنهاء الجلسات (Logout Session).

### المهام

1. **قاعدة البيانات**

   * جدول `audit_logs`:

     * الحقول: `id`, `user_id`, `action` (CREATE/UPDATE/DELETE/LOGIN/LOGOUT), `entity` (مثل: “student”, “bus”, “route”), `entity_id`, `old_data` (JSON)، `new_data` (JSON), `timestamp`, `tenant_id`, `ip_address`, `user_agent`.
   * جدول `user_sessions`:

     * الحقول: `id`, `user_id`, `session_token`, `created_at`, `last_active_at`, `ip_address`, `user_agent`, `is_revoked` (boolean), `tenant_id`.
2. **الواجهة الخلفية**

   * إعداد Trigger أو Middleware في Supabase Functions أو Laravel لتنفيذ الآتي عند كل تغيير (INSERT/UPDATE/DELETE) على الجداول الرئيسية:

     * التقاط بيانات السجل قبل التغيير (`old_data`) وبعده (`new_data`).
     * حفظها في `audit_logs` مع `action`, `entity`, `entity_id`, `user_id` الذي قام بالتغيير (مأخوذ من الـ JWT)، `timestamp`, `tenant_id`, `ip_address`, `user_agent`.
   * في عمليات تسجيل الدخول/الخروج:

     * عند نجاح تسجيل الدخول: إنشاء سجل في `user_sessions` بحقل `session_token`, `ip_address`, `user_agent`.
     * عند تسجيل الخروج: تحديث السجل ليصبح `is_revoked = true`.
   * Endpoint لإدارة الجلسات النشطة:

     * `GET /api/sessions?user_id=...` لإظهار جميع الجلسات (Active/Revoked).
     * `POST /api/sessions/revoke` لاستقبال `{ session_id }` وتعيين `is_revoked = true`.
   * Endpoint لإظهار سجل المراجعة:

     * `GET /api/audit-logs?tenant_id=...&entity=...&date_from=...&date_to=...` يُعيد JSON لسجلات المراجعة مع فلترة حسب الكيان والتاريخ.
3. **الواجهة الأمامية**

   * **صفحة AuditLogs.tsx** (System Admin & School Admin):

     * فورم فلترة: اختيار الكيان (`entity`)، النطاق الزمني (`date_from`, `date_to`)، اسم المستخدم (`user_id`).
     * جدول يعرض: التاريخ، المستخدم، الإجراء (Action)، الكيان، الحقل المعدل، عرض أيقونة للتفاصيل.
     * عند الضغط على تفاصيل: فتح Modal يعرض `old_data` و`new_data` بشكل مصفوفة JSON Tree (باستخدام مكتبة مثل `react-json-view`).
   * **صفحة SessionsManagement.tsx** (User Profile & Admin):

     * جدول يعرض جميع الجلسات الحالية والسابقة (Session ID، تاريخ الإنشاء، آخر نشاط، IP، الجهاز، حالة الجلسة).
     * زر “إنهاء الجلسة” لكل سطر (ينفّذ `/api/sessions/revoke`).
4. **توثيق وتصميم**

   * **docs/details/audit-sessions.md**: شرح جدول `audit_logs`, `user_sessions`, كيفية التقاط البيانات في Middleware/Triggers، بنية JSON المحفوظة في `old_data` و`new_data`.
   * **docs/changelog/audit-sessions.md**: سجل إنشاء الجداول والTriggers والواجهات.
5. **اختبارات**

   * اختبار عملية تعديل بيانات طالب وتحقق من إنشاء سجل في `audit_logs` يحتوي على `old_data` و`new_data`.
   * اختبار قائمة الجلسات لرؤية الجلسات الفعالة والسابقة، وتجربة إنهاء جلسة والتأكد من عدم صلاحية التوكن بعدها.
   * اختبار فلترة سجل المراجعة بناءً على فترة زمنية/كيان محدد والتأكد من عرض النتائج الصحيحة.

### الوثائق المطلوبة

* **docs/changelog/audit-sessions.md**
* **docs/details/audit-sessions.md**

---

## المرحلة 27: اختبارات الأداء والأمان (Performance & Security Testing)

### الهدف الوظيفي

* حسم أداء النظام تحت ظروف حمل عالية (Stress Test, Load Test).
* إجراء تدقيق أمني (Penetration Testing) للتأكد من عدم وجود ثغرات.

### المهام

1. **اختبارات الأداء (Load & Stress Testing)**

   * استخدام أداة مثل k6 أو JMeter لإنشاء سكربت محاكاة لأكثر من 1000 طلب في الثانية.
   * السيناريوهات:

     * تسجيل الدخول المتزامن لعدد كبير من المستخدمين.
     * إضافة طلاب وحافلات ومسارات (CRUD) بشكل متزامن.
     * تحديث موقع الحافلة في Realtime للعديد من الحافلات.
   * تسجيل مؤشرات الأداء (Response Time, Throughput, Error Rate) لمعرفة أين تكون الاختناقات (DB, API, Caching).
   * ضبط Caching باستخدام Redis للبيانات غير الحساسة (مثل قائمة المدارس، المسارات الثابتة) لتقليل الضغط على DB.
2. **اختبارات الأمان (Penetration Testing & Vulnerability Scanning)**

   * تشغيل أداة OWASP ZAP أو Nessus للتدقيق على الثغرات (SQL Injection, XSS, CSRF, RCE, Misconfiguration).
   * اختبار محاولة تجاوز سياسة RLS: إرسال طلب GET إلى `/api/students?tenant_id=other-tenant` والتأكد من رفضه (401/403).
   * اختبار Rate Limiting لتسجيل الدخول: محاولة تنفيذ أكثر من 5 محاولات خاطئة خلال دقيقة والتأكد من حظر IP مؤقتًا.
   * اختبار المصادقة الثنائية: محاولة تسجيل الدخول دون 2FA للرول الملزم بها (مثل System Admin).
3. **تعديلات الأداء والأمان**

   * إضافة Indexes في Postgres على الحقول المستخدمة للتصفية (مثل `tenant_id`، `updated_at`).
   * ضبط Connection Pooling في Supabase أو DB لاقصى عدد مناسب (مثلاً: 20-30).
   * تعزيز إعدادات CORS في الواجهة الخلفية للسماح للـ Frontend فقط بالمصادر الموثقة.
   * تفعيل HSTS (HTTP Strict Transport Security) وContent Security Policy (CSP) على مستوى الـ Edge (إن وجد).
4. **توثيق وتصميم**

   * **docs/details/performance-security.md**:

     * نتائج اختبارات الأداء (باستخدام الرسوم البيانية أو الجداول).
     * قائمة الثغرات الأمنية التي تم العثور عليها وكيف تم حلها.
     * مقترحات تحسينات مستقبلية.
   * **docs/changelog/performance-security.md**: سجل التعديلات المرتبطة بالأداء والأمان (مثلاً: إضافة Index، تحديث إعدادات CORS).
5. **المخرجات**

   * تقرير نهائي (PDF) يتضمن:

     * ملخص أداء النظام تحت الضغط.
     * قائمة الثغرات الأمنية المكتشفة وحالة الحل (Mitigated/Resolved).
     * توصيات لتحسين البنية المستقبلية (Scalability, Fault Tolerance).

### الوثائق المطلوبة

* **docs/changelog/performance-security.md**
* **docs/details/performance-security.md**

---

## المرحلة 28: التجميع النهائي والإنتاج (Final Packaging & Deployment)

### الهدف الوظيفي

* تجميع المشروع بشكل نهائي جاهز للنشر (Build)، وإنشاء Pipeline للنشر التلقائي (CI/CD).
* نشر النظام في بيئة الإنتاج وضبط مراقبة الأنظمة (Monitoring & Logging).

### المهام

1. **إعداد CI/CD**

   * إنشاء ملف GitHub Actions (أو GitLab CI) للنشر التلقائي:

     ```yaml
     name: CI/CD Pipeline

     on:
       push:
         branches: [ main ]

     jobs:
       build-frontend:
         runs-on: ubuntu-latest
         steps:
           - uses: actions/checkout@v3
           - name: Setup Node.js
             uses: actions/setup-node@v3
             with:
               node-version: '18'
           - name: Install dependencies (frontend)
             working-directory: ./school-system-frontend
             run: npm install
           - name: Build (frontend)
             working-directory: ./school-system-frontend
             run: npm run build
           - name: Deploy Frontend to Hosting (Vercel/Netlify/S3)
             # اختيارات حسب الخدمة، مع إعداد متغيرات البيئة
             run: npm run deploy # مثال تجريبي

       build-backend:
         runs-on: ubuntu-latest
         steps:
           - uses: actions/checkout@v3
           - name: Setup Deno/Supabase CLI
             run: |
               curl -fsSL https://deno.land/x/install/install.sh | sh
               npm install -g supabase
           - name: Deploy Supabase Functions
             run: supabase functions deploy
           - name: Apply Database Migrations
             run: supabase db push
       monitor:
         runs-on: ubuntu-latest
         steps:
           - name: Setup Monitoring Alerts
             run: |
               # أمثلة على ضبط Alerts (يمكن استخدام أدوات مثل Datadog أو NewRelic)
               echo "Setting up monitoring..."
     ```
   * إضافة ملف `docker-compose.yml` إن رغبت للحاويات (إذا قررت تجزئة الخدمات في Docker).
2. **إعداد بيئة الإنتاج**

   * تحضير متغيرات البيئة في لوحة Supabase/Netlify/Vercel (SUPABASE\_URL, SUPABASE\_KEY, MAPBOX\_TOKEN, PAYMOB\_CREDENTIALS, إلخ).
   * رفع النسخ النهائية للـ Functions إلى Supabase.
   * نشر الواجهة الأمامية إلى استضافة ثابتة (Vercel/Netlify) مع ضبط نطاق مخصص (CNAME) لكل مدرسة (Subdomain).
   * إعداد SSL/TLS (HTTPS) لجميع النطاقات الفرعية.
3. **ضبط المراقبة واللوغز**

   * ضبط Supabase Logs (Edge Functions Logs) لرؤية الأخطاء في الوقت الفعلي.
   * دمج خدمة مراقبة خارجية (مثل: Datadog, Sentry) لاستقبال الأخطاء (Frontend & Backend).
   * إعداد Alerts لارتفاع نسبة الخطأ (Error Rate > 5% في 5 دقائق).
   * تضمين إعداد Logging داخلي لكل Function (using `console.log`) وحفظه في Storage خارجي (إن لزم الأمر).
4. **تدقيق نهائي (Smoke Test)**

   * تنفيذ اختبارات Smoke (باستخدام Postman أو Cypress):

     * تسجيل الدخول/التسجيل.
     * إنشاء Tenant جديد ومستخدم جديد.
     * إضافة طالب وحافلة ومسار.
     * تشغيل التتبع (GPS) والتأكد من ظهور الموقع.
     * تسجيل حضور عبر QR.
     * إرسال إشعار وتجربته.
     * توليد تقرير والحصول على رابط الفاتورة PDF.
   * التأكد من أن جميع الروابط تعمل (404/500).
5. **توثيق وتصميم**

   * **docs/details/deployment.md**: شرح ملف CI/CD، طريقة النشر اليدوي إذا دعت الحاجة، إعداد المتغيرات في بيئة الإنتاج.
   * **docs/changelog/deployment.md**: سجل النهائيات (كود الإصدار v1.0.0)، أي تعديلات إضافية قبل الإطلاق.
   * تحديث **README.md** بإرشادات استخدام النسخة المنتجة (URLs، Credentials الافتراضية).
6. **إطلاق الإنتاج (Go Live)**

   * إعلان رسمي للمدارس (إذا كان مشروعًا حقيقيًا): إرسال بريد إلكتروني للـ System Admin وSchool Admin يحتوي على رابط النظام وبيانات تسجيل الدخول التجريبية.
   * متابعة الدعم الفني للساعة الأولى لمعالجة أي أعطال غير متوقعة.
   * جدولة اجتماعات قصيرة (Standup) في اليوم الأول لمراقبة الأداء والمشاكل.

### الوثائق المطلوبة

* **docs/changelog/deployment.md**
* **docs/details/deployment.md**

---

## خاتمة

بهذا ينتهي تقسيم المراحل الرئيسية لمشروع **نظام إدارة الحافلات المدرسية كنظام SaaS متعدد المستأجرين**. تم تنظيم كل مرحلة بحيث تغطي بناءً وظيفيًا وتقنيًا متكاملًا، مع التأكيد على عدم تداخل النقاط وتوضيح المهام والوظائف لكل مرحلة بشكل منفصل. كما حرصنا على التوثيق المفصّل لكلّ تغيير في ملفات `docs/changelog/[module].md` و`docs/details/[module].md` لضمان الشفافية وسهولة المتابعة في المستقبل.

بعد هذه المراحل، يكون المشروع جاهزًا للاستخدام والإصدار الرسمي (v1.0.0)، ويمكن للشركة أو الفريق الانتقال إلى مرحلة الصيانة المستمرة وإضافة ميزات جديدة مستقبليًا وفق أولويات العمل ومتطلبات العملاء.

نتمنى لك التوفيق في التنفيذ وجميع المراحل موثقة بشكل احترافي يسهل على أي مطور الانضمام واستكمال العمل لاحقًا.
