const https = require('https');
const fs = require('fs');

const PROJECT_ID = 'lfvnkfzlztjnwyluzoii';
const TOKEN = '********************************************';
const API_URL = `https://api.supabase.com/v1/projects/${PROJECT_ID}/database/query`;

// دالة تنفيذ SQL
function executeSQL(query, description) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({ query });
    
    const options = {
      hostname: 'api.supabase.com',
      port: 443,
      path: `/v1/projects/${PROJECT_ID}/database/query`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`,
        'Content-Length': Buffer.byteLength(data)
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ ${description} - تم بنجاح`);
          resolve(JSON.parse(responseData));
        } else {
          console.log(`❌ ${description} - فشل: ${responseData}`);
          reject(new Error(responseData));
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ خطأ في الطلب: ${error.message}`);
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// تنفيذ الترحيل السادس - البيانات الأساسية
async function migrate006() {
  const sql = `
-- Migration: 006_seed_data.sql
SET search_path TO school_bus_system, public;

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, name_ar, name_en, description, role_scope, is_system_role) VALUES
('system_admin', 'مدير النظام', 'System Admin', 'مدير النظام العام', 'global', true),
('school_admin', 'مدير المدرسة', 'School Admin', 'مدير المدرسة', 'tenant', false),
('driver', 'سائق', 'Driver', 'سائق الحافلة', 'assigned', false),
('parent', 'ولي أمر', 'Parent', 'ولي أمر الطالب', 'children', false),
('student', 'طالب', 'Student', 'طالب', 'personal', false),
('traffic_supervisor', 'مشرف حركة', 'Traffic Supervisor', 'مشرف حركة المرور', 'tenant', false)
ON CONFLICT (name) DO NOTHING;

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, name_ar, name_en, description, module, action, resource) VALUES
-- صلاحيات إدارة النظام
('SYSTEM_MANAGE_ALL', 'إدارة النظام الكاملة', 'Full System Management', 'إدارة كاملة للنظام', 'system', 'manage', 'all'),
('TENANTS_MANAGE', 'إدارة المدارس', 'Manage Schools', 'إدارة المدارس والمستأجرين', 'tenants', 'manage', 'tenants'),

-- صلاحيات إدارة المستخدمين
('USERS_VIEW', 'عرض المستخدمين', 'View Users', 'عرض قائمة المستخدمين', 'users', 'view', 'users'),
('USERS_CREATE', 'إنشاء مستخدمين', 'Create Users', 'إنشاء مستخدمين جدد', 'users', 'create', 'users'),
('USERS_UPDATE', 'تعديل المستخدمين', 'Update Users', 'تعديل بيانات المستخدمين', 'users', 'update', 'users'),
('USERS_DELETE', 'حذف المستخدمين', 'Delete Users', 'حذف المستخدمين', 'users', 'delete', 'users'),

-- صلاحيات إدارة الطلاب
('STUDENTS_VIEW', 'عرض الطلاب', 'View Students', 'عرض قائمة الطلاب', 'students', 'view', 'students'),
('STUDENTS_CREATE', 'إنشاء طلاب', 'Create Students', 'إنشاء طلاب جدد', 'students', 'create', 'students'),
('STUDENTS_UPDATE', 'تعديل الطلاب', 'Update Students', 'تعديل بيانات الطلاب', 'students', 'update', 'students'),
('STUDENTS_DELETE', 'حذف الطلاب', 'Delete Students', 'حذف الطلاب', 'students', 'delete', 'students'),

-- صلاحيات إدارة الحافلات
('BUSES_VIEW', 'عرض الحافلات', 'View Buses', 'عرض قائمة الحافلات', 'buses', 'view', 'buses'),
('BUSES_CREATE', 'إنشاء حافلات', 'Create Buses', 'إنشاء حافلات جديدة', 'buses', 'create', 'buses'),
('BUSES_UPDATE', 'تعديل الحافلات', 'Update Buses', 'تعديل بيانات الحافلات', 'buses', 'update', 'buses'),
('BUSES_DELETE', 'حذف الحافلات', 'Delete Buses', 'حذف الحافلات', 'buses', 'delete', 'buses'),

-- صلاحيات إدارة المسارات
('ROUTES_VIEW', 'عرض المسارات', 'View Routes', 'عرض قائمة المسارات', 'routes', 'view', 'routes'),
('ROUTES_CREATE', 'إنشاء مسارات', 'Create Routes', 'إنشاء مسارات جديدة', 'routes', 'create', 'routes'),
('ROUTES_UPDATE', 'تعديل المسارات', 'Update Routes', 'تعديل بيانات المسارات', 'routes', 'update', 'routes'),
('ROUTES_DELETE', 'حذف المسارات', 'Delete Routes', 'حذف المسارات', 'routes', 'delete', 'routes'),

-- صلاحيات الحضور
('ATTENDANCE_VIEW', 'عرض الحضور', 'View Attendance', 'عرض سجلات الحضور', 'attendance', 'view', 'attendance'),
('ATTENDANCE_RECORD', 'تسجيل الحضور', 'Record Attendance', 'تسجيل حضور الطلاب', 'attendance', 'create', 'attendance'),

-- صلاحيات التقارير
('REPORTS_VIEW', 'عرض التقارير', 'View Reports', 'عرض التقارير والإحصائيات', 'reports', 'view', 'reports'),
('REPORTS_EXPORT', 'تصدير التقارير', 'Export Reports', 'تصدير التقارير', 'reports', 'export', 'reports')
ON CONFLICT (name) DO NOTHING;

SELECT 'Migration 006 completed successfully' as result;
`;

  await executeSQL(sql, 'الترحيل السادس: البيانات الأساسية');
}

// تنفيذ الترحيل السابع - البيانات التجريبية
async function migrate007() {
  const sql = `
-- Migration: 007_sample_data.sql
SET search_path TO school_bus_system, public;

-- ربط الأدوار بالصلاحيات - مدير النظام
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'system_admin'),
    id
FROM permissions
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- ربط الأدوار بالصلاحيات - مدير المدرسة
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'school_admin'),
    id
FROM permissions
WHERE name IN (
    'USERS_VIEW', 'USERS_CREATE', 'USERS_UPDATE',
    'STUDENTS_VIEW', 'STUDENTS_CREATE', 'STUDENTS_UPDATE', 'STUDENTS_DELETE',
    'BUSES_VIEW', 'BUSES_CREATE', 'BUSES_UPDATE',
    'ROUTES_VIEW', 'ROUTES_CREATE', 'ROUTES_UPDATE', 'ROUTES_DELETE',
    'ATTENDANCE_VIEW', 'ATTENDANCE_RECORD',
    'REPORTS_VIEW', 'REPORTS_EXPORT'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- إضافة مدرسة تجريبية (إذا لم تكن موجودة)
INSERT INTO tenants (
    id, name, domain, subdomain, 
    primary_color, secondary_color, accent_color,
    enabled_modules, settings
) VALUES (
    '550e8400-e29b-41d4-a716-446655440001',
    'مدرسة المستقبل النموذجية',
    'future-model-school.edu.sa',
    'future-school',
    '#1e40af',
    '#059669', 
    '#dc2626',
    ARRAY['auth', 'users', 'students', 'parents', 'drivers', 'buses', 'routes', 'attendance', 'notifications'],
    '{"school_type": "public", "grade_levels": ["KG1", "KG2", "1", "2", "3", "4", "5", "6"]}'
) ON CONFLICT (id) DO NOTHING;

SELECT 'Migration 007 completed successfully' as result;
`;

  await executeSQL(sql, 'الترحيل السابع: البيانات التجريبية');
}

// تنفيذ الترحيل الثامن - Views والدوال
async function migrate008() {
  const sql = `
-- Migration: 008_views_and_functions.sql
SET search_path TO school_bus_system, public;

-- دالة للتحقق من صحة قاعدة البيانات
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    details TEXT,
    count_value BIGINT
) AS $$
BEGIN
    -- فحص الجداول الأساسية
    RETURN QUERY
    SELECT 
        'Tables Count'::TEXT,
        'OK'::TEXT,
        'All core tables exist'::TEXT,
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = 'school_bus_system')::BIGINT;
    
    -- فحص المستأجرين
    RETURN QUERY
    SELECT 
        'Tenants'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::TEXT,
        ('Total tenants: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM tenants;
    
    -- فحص الأدوار
    RETURN QUERY
    SELECT 
        'Roles'::TEXT,
        CASE WHEN COUNT(*) >= 6 THEN 'OK' ELSE 'WARNING' END::TEXT,
        ('Total roles: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM roles;
    
    -- فحص الصلاحيات
    RETURN QUERY
    SELECT 
        'Permissions'::TEXT,
        CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::TEXT,
        ('Total permissions: ' || COUNT(*))::TEXT,
        COUNT(*)::BIGINT
    FROM permissions;
             
END;
$$ LANGUAGE plpgsql;

SELECT 'Migration 008 completed successfully' as result;
`;

  await executeSQL(sql, 'الترحيل الثامن: Views والدوال');
}

// الدالة الرئيسية
async function main() {
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    🚀 ترحيل قاعدة البيانات                    ║
║                  School Bus Database Migration               ║
╚══════════════════════════════════════════════════════════════╝
`);

  try {
    console.log('🔄 بدء الترحيل التلقائي...');
    
    await migrate006();
    await migrate007();
    await migrate008();
    
    // فحص صحة النظام
    console.log('🔍 فحص صحة النظام...');
    await executeSQL('SELECT * FROM school_bus_system.database_health_check();', 'فحص صحة قاعدة البيانات');
    
    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                        ✅ تم الانتهاء!                        ║
║                     Migration Complete!                     ║
╚══════════════════════════════════════════════════════════════╝

🎉 تم إكمال الترحيل بنجاح!
📊 النظام جاهز بنسبة 100%
🔗 رابط المشروع: https://supabase.com/dashboard/project/${PROJECT_ID}

الخطوات التالية:
1. احصل على مفاتيح API من لوحة التحكم
2. أعد إعداد متغيرات البيئة
3. ابدأ التطوير!
`);

  } catch (error) {
    console.error('❌ فشل في الترحيل:', error.message);
  }
}

main();
