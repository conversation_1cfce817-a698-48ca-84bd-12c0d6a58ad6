-- Migration: 005_rls_policies.sql
-- Description: تفعيل Row Level Security وإنشاء سياسات العزل بين المستأجرين
-- Created: 2024-01-15
-- Author: School Bus Management System

SET search_path TO school_bus_system, public;

-- تفعيل RLS على جميع الجداول الحساسة
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_logs ENABLE ROW LEVEL SECURITY;

-- سياسات العزل بين المستأجرين

-- سياسة المستخدمين
CREATE POLICY "users_tenant_isolation" ON users
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة الطلاب
CREATE POLICY "students_tenant_isolation" ON students
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة السائقين
CREATE POLICY "drivers_tenant_isolation" ON drivers
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة الحافلات
CREATE POLICY "buses_tenant_isolation" ON buses
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة المسارات
CREATE POLICY "routes_tenant_isolation" ON routes
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة نقاط التوقف (عبر المسار)
CREATE POLICY "stops_tenant_isolation" ON stops
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM routes r 
            WHERE r.id = route_id 
            AND (r.tenant_id = get_current_tenant_id() OR get_current_tenant_id() IS NULL)
        )
    );

-- سياسة الحضور
CREATE POLICY "attendance_tenant_isolation" ON attendance
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة الإشعارات
CREATE POLICY "notifications_tenant_isolation" ON notifications
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة جلسات المصادقة
CREATE POLICY "auth_sessions_tenant_isolation" ON auth_sessions
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة سجل تسجيل الدخول
CREATE POLICY "login_logs_tenant_isolation" ON login_logs
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

-- سياسة خاصة للمستأجرين (لمديري النظام فقط)
CREATE POLICY "tenants_system_admin_only" ON tenants
    FOR ALL USING (
        -- يمكن لمديري النظام رؤية جميع المستأجرين
        -- أو للمستأجر رؤية بياناته فقط
        get_current_tenant_id() IS NULL OR
        id = get_current_tenant_id()
    );

-- سياسات خاصة للأدوار والصلاحيات (عامة للقراءة، محدودة للكتابة)
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permission_map ENABLE ROW LEVEL SECURITY;

-- سياسة الأدوار - قراءة عامة، كتابة محدودة
CREATE POLICY "roles_read_all" ON roles
    FOR SELECT USING (true);

CREATE POLICY "roles_write_system_admin" ON roles
    FOR INSERT, UPDATE, DELETE USING (
        get_current_tenant_id() IS NULL -- مديري النظام فقط
    );

-- سياسة الصلاحيات - قراءة عامة، كتابة محدودة
CREATE POLICY "permissions_read_all" ON permissions
    FOR SELECT USING (true);

CREATE POLICY "permissions_write_system_admin" ON permissions
    FOR INSERT, UPDATE, DELETE USING (
        get_current_tenant_id() IS NULL -- مديري النظام فقط
    );

-- سياسة ربط الأدوار بالصلاحيات - قراءة عامة، كتابة محدودة
CREATE POLICY "role_permission_map_read_all" ON role_permission_map
    FOR SELECT USING (true);

CREATE POLICY "role_permission_map_write_system_admin" ON role_permission_map
    FOR INSERT, UPDATE, DELETE USING (
        get_current_tenant_id() IS NULL -- مديري النظام فقط
    );

-- سياسات خاصة للمستخدمين حسب الأدوار

-- سياسة للآباء - يمكنهم رؤية أطفالهم فقط
CREATE POLICY "parents_see_own_children" ON students
    FOR SELECT USING (
        parent_id = auth.uid() OR
        (tenant_id = get_current_tenant_id() AND 
         EXISTS (
             SELECT 1 FROM users u 
             WHERE u.id = auth.uid() 
             AND u.role_id IN (
                 SELECT id FROM roles 
                 WHERE name IN ('school_admin', 'system_admin')
             )
         ))
    );

-- سياسة للسائقين - يمكنهم رؤية طلاب حافلاتهم فقط
CREATE POLICY "drivers_see_assigned_students" ON attendance
    FOR SELECT USING (
        bus_id IN (
            SELECT id FROM buses 
            WHERE driver_id IN (
                SELECT id FROM drivers 
                WHERE tenant_id = get_current_tenant_id()
            )
        ) OR
        (tenant_id = get_current_tenant_id() AND 
         EXISTS (
             SELECT 1 FROM users u 
             WHERE u.id = auth.uid() 
             AND u.role_id IN (
                 SELECT id FROM roles 
                 WHERE name IN ('school_admin', 'system_admin')
             )
         ))
    );

-- سياسة للطلاب - يمكنهم رؤية بياناتهم الشخصية فقط
CREATE POLICY "students_see_own_data" ON attendance
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM students 
            WHERE tenant_id = get_current_tenant_id()
        ) OR
        (tenant_id = get_current_tenant_id() AND 
         EXISTS (
             SELECT 1 FROM users u 
             WHERE u.id = auth.uid() 
             AND u.role_id IN (
                 SELECT id FROM roles 
                 WHERE name IN ('school_admin', 'system_admin', 'driver')
             )
         ))
    );

-- إضافة التعليقات على السياسات
COMMENT ON POLICY "users_tenant_isolation" ON users IS 'عزل المستخدمين حسب المستأجر';
COMMENT ON POLICY "students_tenant_isolation" ON students IS 'عزل الطلاب حسب المستأجر';
COMMENT ON POLICY "drivers_tenant_isolation" ON drivers IS 'عزل السائقين حسب المستأجر';
COMMENT ON POLICY "buses_tenant_isolation" ON buses IS 'عزل الحافلات حسب المستأجر';
COMMENT ON POLICY "routes_tenant_isolation" ON routes IS 'عزل المسارات حسب المستأجر';
COMMENT ON POLICY "stops_tenant_isolation" ON stops IS 'عزل نقاط التوقف حسب المستأجر عبر المسار';
COMMENT ON POLICY "attendance_tenant_isolation" ON attendance IS 'عزل بيانات الحضور حسب المستأجر';
COMMENT ON POLICY "notifications_tenant_isolation" ON notifications IS 'عزل الإشعارات حسب المستأجر';
COMMENT ON POLICY "tenants_system_admin_only" ON tenants IS 'المستأجرين - مديري النظام أو المستأجر نفسه فقط';
COMMENT ON POLICY "parents_see_own_children" ON students IS 'الآباء يرون أطفالهم فقط';
COMMENT ON POLICY "drivers_see_assigned_students" ON attendance IS 'السائقين يرون طلاب حافلاتهم فقط';
COMMENT ON POLICY "students_see_own_data" ON attendance IS 'الطلاب يرون بياناتهم الشخصية فقط';
