# سكربت ترحيل مبسط
Write-Host "🚀 بدء الترحيل..." -ForegroundColor Blue

# قراءة ملف SQL
$sqlContent = Get-Content -Path "complete_migration.sql" -Raw -Encoding UTF8

# إعداد الطلب
$headers = @{
    'Authorization' = 'Bearer ********************************************'
    'Content-Type' = 'application/json'
}

$body = @{
    query = $sqlContent
} | ConvertTo-Json -Depth 10

# تنفيذ الطلب
try {
    Write-Host "📤 إرسال البيانات..." -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri 'https://api.supabase.com/v1/projects/lfvnkfzlztjnwyluzoii/database/query' -Method POST -Headers $headers -Body $body -TimeoutSec 60
    
    Write-Host "✅ تم الترحيل بنجاح!" -ForegroundColor Green
    Write-Host "النتيجة: $($response[-1].result)" -ForegroundColor Cyan
    
    # فحص الصحة
    Write-Host "`n🔍 فحص الصحة..." -ForegroundColor Blue
    
    $healthBody = @{
        query = "SELECT * FROM school_bus_system.database_health_check();"
    } | ConvertTo-Json
    
    $healthResponse = Invoke-RestMethod -Uri 'https://api.supabase.com/v1/projects/lfvnkfzlztjnwyluzoii/database/query' -Method POST -Headers $headers -Body $healthBody
    
    Write-Host "`n📊 النتائج:" -ForegroundColor Green
    foreach ($check in $healthResponse) {
        Write-Host "✅ $($check.check_name): $($check.details)" -ForegroundColor Green
    }
    
    Write-Host "`n🎉 اكتمل الترحيل 100%!" -ForegroundColor Magenta
    Write-Host "🔗 https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
}
