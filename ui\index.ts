/**
 * نقطة الدخول الرئيسية لمكتبة واجهة المستخدم
 * UI Library Entry Point
 */

// المكونات الأساسية (Base Components)
export { Button } from './components/base/Button';
export { Input } from './components/base/Input';
export { Typography, Heading, Text } from './components/base/Typography';
export { Avatar, AvatarImage, AvatarFallback } from './components/base/Avatar';
export { Badge } from './components/base/Badge';
export { Separator } from './components/base/Separator';

// مكونات النماذج (Form Components)
export { FormField } from './components/forms/FormField';
export { Select, SelectOption } from './components/forms/Select';
export { Checkbox } from './components/forms/Checkbox';
export { Radio, RadioGroup } from './components/forms/Radio';
export { DatePicker } from './components/forms/DatePicker';
export { FileUpload } from './components/forms/FileUpload';
export { SearchInput } from './components/forms/SearchInput';
export { TextArea } from './components/forms/TextArea';

// مكونات التنقل (Navigation Components)
export { Sidebar, SidebarItem, SidebarSection } from './components/navigation/Sidebar';
export { Breadcrumb, BreadcrumbItem } from './components/navigation/Breadcrumb';
export { Tabs, TabsList, TabsTrigger, TabsContent } from './components/navigation/Tabs';
export { Pagination } from './components/navigation/Pagination';
export { Menu, MenuItem, MenuSeparator } from './components/navigation/Menu';

// مكونات عرض البيانات (Data Display Components)
export { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from './components/data-display/Table';
export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './components/data-display/Card';
export { DataTable } from './components/data-display/DataTable';
export { EmptyState } from './components/data-display/EmptyState';
export { StatCard } from './components/data-display/StatCard';
export { Timeline, TimelineItem } from './components/data-display/Timeline';

// مكونات التغذية الراجعة (Feedback Components)
export { Alert, AlertIcon, AlertTitle, AlertDescription } from './components/feedback/Alert';
export { Toast, useToast } from './components/feedback/Toast';
export { Loading, LoadingSpinner, LoadingSkeleton } from './components/feedback/Loading';
export { Progress, ProgressCircular } from './components/feedback/Progress';
export { ErrorBoundary } from './components/feedback/ErrorBoundary';

// مكونات التخطيط (Layout Components)
export { AppLayout } from './layout/AppLayout';
export { Header, HeaderLogo, HeaderNavigation, HeaderActions } from './layout/Header';
export { MainContent, ContentHeader, ContentBody } from './layout/MainContent';
export { Footer, FooterContent, FooterSection } from './layout/Footer';
export { Container } from './layout/Container';
export { Grid, GridItem } from './layout/Grid';
export { Flex, FlexItem } from './layout/Flex';
export { Stack, VStack, HStack } from './layout/Stack';
export { Spacer } from './layout/Spacer';

// مكونات الوسائط (Media Components)
export { Image } from './components/media/Image';
export { Video } from './components/media/Video';
export { Icon } from './components/media/Icon';
export { QRCode } from './components/media/QRCode';

// مكونات التراكب (Overlay Components)
export { Modal, ModalHeader, ModalTitle, ModalBody, ModalFooter } from './components/overlays/Modal';
export { Dropdown, DropdownTrigger, DropdownContent, DropdownItem } from './components/overlays/Dropdown';
export { Tooltip } from './components/overlays/Tooltip';
export { Popover, PopoverTrigger, PopoverContent } from './components/overlays/Popover';
export { Sheet, SheetTrigger, SheetContent, SheetHeader, SheetTitle } from './components/overlays/Sheet';

// المكونات المتخصصة (Specialized Components)
export { MapComponent } from './components/specialized/MapComponent';
export { QRCodeScanner } from './components/specialized/QRCodeScanner';
export { Chart } from './components/specialized/Chart';
export { Calendar } from './components/specialized/Calendar';
export { ColorPicker } from './components/specialized/ColorPicker';
export { RichTextEditor } from './components/specialized/RichTextEditor';

// نظام الثيمات (Theme System)
export { ThemeProvider } from './themes/ThemeProvider';
export { useTheme } from './themes/useTheme';
export { lightTheme, darkTheme } from './themes';
export { createSchoolTheme } from './themes/createSchoolTheme';

// الأدوات والمساعدات (Utils & Helpers)
export { cn } from './utils/cn';
export { formatters } from './utils/formatters';
export { validators } from './utils/validators';
export { helpers } from './utils/helpers';
export { UI_CONSTANTS } from './utils/constants';

// React Hooks المخصصة
export { useLocalStorage } from './utils/hooks/useLocalStorage';
export { useDebounce } from './utils/hooks/useDebounce';
export { useClickOutside } from './utils/hooks/useClickOutside';
export { useMediaQuery } from './utils/hooks/useMediaQuery';
export { useCopyToClipboard } from './utils/hooks/useCopyToClipboard';
export { useToggle } from './utils/hooks/useToggle';
export { usePrevious } from './utils/hooks/usePrevious';
export { useInterval } from './utils/hooks/useInterval';
export { useTimeout } from './utils/hooks/useTimeout';
export { useOnlineStatus } from './utils/hooks/useOnlineStatus';
export { useKeyPress } from './utils/hooks/useKeyPress';
export { useScrollPosition } from './utils/hooks/useScrollPosition';
export { useWindowSize } from './utils/hooks/useWindowSize';

// أنواع البيانات (Types)
export type {
  BaseProps,
  ComponentVariants,
  LoadingState,
  PaginationProps,
  SortConfig,
  FilterConfig,
  TableColumn,
  FormFieldProps,
  ToastOptions,
  ThemeConfig,
  ColorScheme,
  BreakpointConfig,
} from './utils/types';

// الحركات والانتقالات (Animations)
export { fadeIn, slideUp, slideDown, slideLeft, slideRight } from './utils/animations';
export { AnimatePresence, motion } from 'framer-motion';

// مزودي السياق (Context Providers)
export { LanguageProvider, useLanguage } from './contexts/LanguageContext';
export { DirectionProvider, useDirection } from './contexts/DirectionContext';
export { NotificationProvider, useNotification } from './contexts/NotificationContext';

// الثوابت العامة
export const UI_VERSION = '1.0.0';
export const SUPPORTED_LANGUAGES = ['ar', 'en'] as const;
export const DEFAULT_LANGUAGE = 'ar';
export const DEFAULT_DIRECTION = 'rtl';

/**
 * تهيئة مكتبة واجهة المستخدم
 * Initialize UI Library
 */
export function initializeUI(config?: {
  theme?: 'light' | 'dark' | 'auto';
  language?: 'ar' | 'en';
  direction?: 'rtl' | 'ltr';
  primaryColor?: string;
  fontFamily?: string;
}) {
  // تطبيق الإعدادات الافتراضية
  const defaultConfig = {
    theme: 'light',
    language: 'ar',
    direction: 'rtl',
    primaryColor: '#3b82f6',
    fontFamily: 'Cairo',
    ...config,
  };

  // تطبيق الثيم
  document.documentElement.setAttribute('data-theme', defaultConfig.theme);
  
  // تطبيق اللغة والاتجاه
  document.documentElement.setAttribute('lang', defaultConfig.language);
  document.documentElement.setAttribute('dir', defaultConfig.direction);
  
  // تطبيق الخط
  document.documentElement.style.setProperty('--font-family', defaultConfig.fontFamily);
  
  // تطبيق اللون الأساسي
  document.documentElement.style.setProperty('--color-primary', defaultConfig.primaryColor);

  return defaultConfig;
}

/**
 * مكونات مجمعة للاستخدام السريع
 * Compound Components for Quick Usage
 */
export const UI = {
  // المكونات الأساسية
  Button,
  Input,
  Text: Typography,
  
  // النماذج
  Form: {
    Field: FormField,
    Select,
    Checkbox,
    Radio: RadioGroup,
    DatePicker,
    FileUpload,
  },
  
  // التخطيط
  Layout: {
    App: AppLayout,
    Header,
    Main: MainContent,
    Footer,
    Container,
    Grid,
    Flex,
    Stack: VStack,
  },
  
  // عرض البيانات
  Data: {
    Table,
    Card,
    Badge,
    Avatar,
  },
  
  // التغذية الراجعة
  Feedback: {
    Alert,
    Toast,
    Loading,
    Progress,
  },
  
  // التراكب
  Overlay: {
    Modal,
    Dropdown,
    Tooltip,
    Popover,
  },
  
  // المتخصصة
  Specialized: {
    Map: MapComponent,
    QRScanner: QRCodeScanner,
    Chart,
    Calendar,
  },
};

// تصدير افتراضي
export default UI;
