-- Migration: 007_sample_data.sql
-- Description: إضافة بيانات تجريبية للحافلات والمسارات والطلاب
-- Created: 2024-01-15
-- Author: School Bus Management System

SET search_path TO school_bus_system, public;

-- إضافة حافلة تجريبية
INSERT INTO buses (
    id, bus_number, plate_number, capacity, model, year,
    manufacturer, color, fuel_type,
    features, driver_id, tenant_id,
    insurance_expiry_date, registration_expiry_date
) VALUES (
    '550e8400-e29b-41d4-a716-446655440004',
    'BUS-001',
    'أ أ أ 1234',
    45,
    'Coaster',
    2023,
    'Toyota',
    'أصفر',
    'ديزل',
    '{
        "air_conditioning": true,
        "wifi": false,
        "gps_tracker": true,
        "security_cameras": true,
        "first_aid_kit": true,
        "fire_extinguisher": true
    }',
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440001',
    '2025-06-30',
    '2025-03-15'
) ON CONFLICT (plate_number) DO NOTHING;

-- إضافة مسار تجريبي
INSERT INTO routes (
    id, name, description, route_type,
    bus_id, driver_id, start_time, end_time,
    working_days, estimated_duration, distance_km, max_students,
    tenant_id
) VALUES (
    '550e8400-e29b-41d4-a716-446655440005',
    'مسار العليا - المركز',
    'مسار يغطي أحياء العليا والمركز',
    'regular',
    '550e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440003',
    '06:30:00',
    '07:15:00',
    ARRAY[1,2,3,4,5], -- الأحد إلى الخميس
    45, -- 45 دقيقة
    15.5, -- 15.5 كم
    40, -- حد أقصى 40 طالب
    '550e8400-e29b-41d4-a716-446655440001'
) ON CONFLICT (id) DO NOTHING;

-- إضافة نقاط توقف تجريبية
INSERT INTO stops (
    route_id, stop_order, location, name_ar, name_en,
    description, landmarks, arrival_time_estimate, stop_type
) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440005',
    1,
    ST_GeogFromText('POINT(46.7219 24.6877)'), -- موقع تجريبي في الرياض
    'مجمع العليا التجاري',
    'Al-Olaya Commercial Complex',
    'نقطة الانطلاق الرئيسية',
    'بجانب محطة البنزين',
    '06:30:00',
    'pickup'
),
(
    '550e8400-e29b-41d4-a716-446655440005',
    2,
    ST_GeogFromText('POINT(46.7319 24.6977)'),
    'حي العليا - الشارع الرئيسي',
    'Al-Olaya District - Main Street',
    'نقطة تجميع الطلاب',
    'أمام مسجد العليا',
    '06:40:00',
    'pickup'
),
(
    '550e8400-e29b-41d4-a716-446655440005',
    3,
    ST_GeogFromText('POINT(46.7419 24.7077)'),
    'مدرسة المستقبل النموذجية',
    'Future Model School',
    'وجهة نهائية - المدرسة',
    'بوابة المدرسة الرئيسية',
    '07:15:00',
    'dropoff'
)
ON CONFLICT (route_id, stop_order) DO NOTHING;

-- إضافة مستخدم ولي أمر تجريبي
INSERT INTO users (
    id, email, name_ar, name_en, phone,
    tenant_id, role_id, is_active, email_verified
) VALUES (
    '550e8400-e29b-41d4-a716-446655440006',
    '<EMAIL>',
    'سارة أحمد المحمد',
    'Sarah Ahmed Al-Mohammed',
    '+966504567890',
    '550e8400-e29b-41d4-a716-446655440001',
    (SELECT id FROM roles WHERE name = 'parent'),
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- إضافة طلاب تجريبيين
INSERT INTO students (
    id, name_ar, name_en, grade, class, student_id,
    parent_id, date_of_birth, address,
    emergency_contact_name, emergency_contact_phone,
    tenant_id
) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440007',
    'عبدالله سارة المحمد',
    'Abdullah Sarah Al-Mohammed',
    'الصف الخامس',
    '5أ',
    'STU-2024-001',
    '550e8400-e29b-41d4-a716-446655440006',
    '2014-03-15',
    'الرياض، حي العليا، شارع الملك فهد',
    'سارة أحمد المحمد',
    '+966504567890',
    '550e8400-e29b-41d4-a716-446655440001'
),
(
    '550e8400-e29b-41d4-a716-446655440008',
    'فاطمة سارة المحمد',
    'Fatima Sarah Al-Mohammed',
    'الصف الثالث',
    '3ب',
    'STU-2024-002',
    '550e8400-e29b-41d4-a716-446655440006',
    '2016-07-22',
    'الرياض، حي العليا، شارع الملك فهد',
    'سارة أحمد المحمد',
    '+966504567890',
    '550e8400-e29b-41d4-a716-446655440001'
)
ON CONFLICT (id) DO NOTHING;

-- إضافة مستخدم سائق
INSERT INTO users (
    id, email, name_ar, name_en, phone,
    tenant_id, role_id, is_active, email_verified
) VALUES (
    '550e8400-e29b-41d4-a716-446655440009',
    '<EMAIL>',
    'محمد عبدالله الأحمد',
    'Mohammed Abdullah Al-Ahmad',
    '+966502345678',
    '550e8400-e29b-41d4-a716-446655440001',
    (SELECT id FROM roles WHERE name = 'driver'),
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- إضافة بعض سجلات الحضور التجريبية
INSERT INTO attendance (
    student_id, bus_id, route_id, stop_id,
    date, timestamp, status, location,
    recorded_by, method, tenant_id
) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440007',
    '550e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440005',
    (SELECT id FROM stops WHERE route_id = '550e8400-e29b-41d4-a716-446655440005' AND stop_order = 2),
    CURRENT_DATE,
    CURRENT_TIMESTAMP,
    'boarded',
    ST_GeogFromText('POINT(46.7319 24.6977)'),
    '550e8400-e29b-41d4-a716-446655440009',
    'manual',
    '550e8400-e29b-41d4-a716-446655440001'
),
(
    '550e8400-e29b-41d4-a716-446655440008',
    '550e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440005',
    (SELECT id FROM stops WHERE route_id = '550e8400-e29b-41d4-a716-446655440005' AND stop_order = 2),
    CURRENT_DATE,
    CURRENT_TIMESTAMP,
    'boarded',
    ST_GeogFromText('POINT(46.7319 24.6977)'),
    '550e8400-e29b-41d4-a716-446655440009',
    'manual',
    '550e8400-e29b-41d4-a716-446655440001'
),
(
    '550e8400-e29b-41d4-a716-446655440007',
    '550e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440005',
    (SELECT id FROM stops WHERE route_id = '550e8400-e29b-41d4-a716-446655440005' AND stop_order = 1),
    CURRENT_DATE - INTERVAL '1 day',
    CURRENT_TIMESTAMP - INTERVAL '1 day',
    'boarded',
    ST_GeogFromText('POINT(46.7219 24.6877)'),
    '550e8400-e29b-41d4-a716-446655440009',
    'manual',
    '550e8400-e29b-41d4-a716-446655440001'
);

-- إضافة إشعارات تجريبية
INSERT INTO notifications (
    type, category, title_ar, title_en, message_ar, message_en,
    recipient_role, priority, delivery_methods,
    tenant_id
) VALUES 
(
    'welcome',
    'general',
    'مرحباً بكم في نظام إدارة الحافلات',
    'Welcome to School Bus Management System',
    'نرحب بكم في نظام إدارة الحافلات المدرسية الجديد. يمكنكم الآن متابعة حضور أطفالكم وتتبع مواقع الحافلات.',
    'Welcome to the new School Bus Management System. You can now track your children''s attendance and bus locations.',
    'parent',
    'medium',
    ARRAY['app', 'email'],
    '550e8400-e29b-41d4-a716-446655440001'
),
(
    'attendance_reminder',
    'attendance',
    'تذكير بتسجيل الحضور',
    'Attendance Recording Reminder',
    'يرجى تذكير الطلاب بضرورة تسجيل الحضور عند ركوب الحافلة.',
    'Please remind students to record their attendance when boarding the bus.',
    'driver',
    'high',
    ARRAY['app'],
    '550e8400-e29b-41d4-a716-446655440001'
);

-- تحديث موقع الحافلة التجريبية
UPDATE buses 
SET 
    current_location = ST_GeogFromText('POINT(46.7319 24.6977)'),
    last_location_update = NOW()
WHERE id = '550e8400-e29b-41d4-a716-446655440004';
