# سكربت الترحيل التلقائي إلى Supabase
# Auto Migration Script to Supabase
# 
# الاستخدام:
# .\scripts\auto-migrate.ps1 -Token "your_access_token"
# 
# أو تعيين متغير البيئة:
# $env:SUPABASE_ACCESS_TOKEN = "your_token"
# .\scripts\auto-migrate.ps1

param(
    [string]$Token = $env:SUPABASE_ACCESS_TOKEN,
    [switch]$SkipExisting = $false
)

# إعدادات المشروع
$ProjectId = "lfvnkfzlztjnwyluzoii"
$ApiBase = "https://api.supabase.com/v1"
$DbApi = "$ApiBase/projects/$ProjectId/database/query"

# دوال المساعدة
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "Cyan" = "Cyan"
        "Magenta" = "Magenta"
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "[STEP] $Message" "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

# فحص حالة المشروع
function Test-ProjectStatus {
    try {
        $headers = @{
            "Authorization" = "Bearer $Token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$ApiBase/projects/$ProjectId" -Method GET -Headers $headers
        
        Write-Success "المشروع نشط: $($response.name) ($($response.status))"
        return $true
    }
    catch {
        Write-Error "فشل في الوصول للمشروع: $($_.Exception.Message)"
        return $false
    }
}

# تنفيذ استعلام SQL
function Invoke-SQLQuery {
    param(
        [string]$Query,
        [string]$Description
    )
    
    try {
        $headers = @{
            "Authorization" = "Bearer $Token"
            "Content-Type" = "application/json"
        }
        
        $body = @{
            query = $Query
        } | ConvertTo-Json -Depth 10
        
        $response = Invoke-RestMethod -Uri $DbApi -Method POST -Headers $headers -Body $body
        
        Write-Success "$Description - تم بنجاح"
        return $response
    }
    catch {
        $errorMessage = $_.Exception.Message
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            $errorMessage += " - $responseBody"
        }
        
        if ($SkipExisting -and $errorMessage -like "*already exists*") {
            Write-Warning "$Description - موجود مسبقاً، تم التخطي"
            return $null
        }
        
        Write-Error "$Description - فشل: $errorMessage"
        throw
    }
}

# قراءة ملف SQL
function Get-SQLFileContent {
    param([string]$FileName)
    
    $filePath = Join-Path $PSScriptRoot "..\supabase\migrations\$FileName"
    
    if (-not (Test-Path $filePath)) {
        throw "ملف الترحيل غير موجود: $FileName"
    }
    
    return Get-Content $filePath -Raw -Encoding UTF8
}

# قائمة ملفات الترحيل
$migrationFiles = @(
    @{ File = "001_initial_setup.sql"; Description = "الإعداد الأساسي والامتدادات" },
    @{ File = "002_core_tables.sql"; Description = "الجداول الأساسية" },
    @{ File = "003_entity_tables.sql"; Description = "جداول الكيانات" },
    @{ File = "004_routes_and_stops.sql"; Description = "المسارات ونقاط التوقف" },
    @{ File = "005_rls_policies.sql"; Description = "سياسات الأمان" },
    @{ File = "006_seed_data.sql"; Description = "البيانات الأساسية" },
    @{ File = "007_sample_data.sql"; Description = "البيانات التجريبية" },
    @{ File = "008_views_and_functions.sql"; Description = "Views والدوال المتقدمة" }
)

# الدالة الرئيسية
function Start-Migration {
    # عرض العنوان
    Write-ColorOutput @"

╔══════════════════════════════════════════════════════════════╗
║                    🚀 ترحيل قاعدة البيانات                    ║
║                  School Bus Database Migration               ║
╚══════════════════════════════════════════════════════════════╝

"@ "Magenta"

    # فحص Access Token
    if (-not $Token) {
        Write-Error "Access Token مطلوب!"
        Write-Info "استخدم: -Token 'your_token' أو تعيين `$env:SUPABASE_ACCESS_TOKEN"
        exit 1
    }

    Write-Info "بدء عملية الترحيل..."

    # فحص حالة المشروع
    Write-Step "فحص حالة المشروع..."
    if (-not (Test-ProjectStatus)) {
        exit 1
    }

    # تنفيذ ملفات الترحيل
    $completedMigrations = 0
    
    foreach ($migration in $migrationFiles) {
        try {
            Write-Step "تنفيذ: $($migration.Description)"
            
            $sqlContent = Get-SQLFileContent $migration.File
            Invoke-SQLQuery -Query $sqlContent -Description $migration.Description
            
            $completedMigrations++
            
            # تأخير قصير بين الترحيلات
            Start-Sleep -Seconds 1
        }
        catch {
            Write-Warning "تخطي $($migration.File): $($_.Exception.Message)"
            # متابعة مع الملفات التالية
        }
    }

    # فحص صحة النظام
    Write-Step "فحص صحة النظام..."
    try {
        Invoke-SQLQuery -Query "SELECT * FROM school_bus_system.database_health_check();" -Description "فحص صحة قاعدة البيانات"
    }
    catch {
        Write-Warning "دالة فحص الصحة غير متاحة بعد"
    }

    # النتائج النهائية
    Write-ColorOutput @"

╔══════════════════════════════════════════════════════════════╗
║                        ✅ تم الانتهاء!                        ║
║                     Migration Complete!                     ║
╚══════════════════════════════════════════════════════════════╝

"@ "Green"

    Write-Success "تم تنفيذ $completedMigrations/$($migrationFiles.Count) ملف ترحيل"
    Write-Info "معرف المشروع: $ProjectId"
    Write-Info "رابط المشروع: https://supabase.com/dashboard/project/$ProjectId"
    
    Write-ColorOutput @"

الخطوات التالية:
1. احصل على مفاتيح API من لوحة التحكم
2. أعد إعداد متغيرات البيئة  
3. ابدأ التطوير!

"@ "Cyan"
}

# تشغيل السكربت
try {
    Start-Migration
}
catch {
    Write-Error "فشل في الترحيل: $($_.Exception.Message)"
    exit 1
}
