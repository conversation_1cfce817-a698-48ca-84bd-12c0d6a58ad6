# سكربتات التحضير والنشر (Scripts)

هذا المجلد يحتوي على جميع السكربتات المساعدة للتطوير والنشر.

## هيكل السكربتات

```
scripts/
├── setup/              # سكربتات الإعداد الأولي
│   ├── init-project.sh # إعداد المشروع
│   ├── setup-db.sql    # إعداد قاعدة البيانات
│   └── seed-data.sql   # بيانات تجريبية
├── build/              # سكربتات البناء
│   ├── build-frontend.sh
│   ├── build-backend.sh
│   └── optimize-assets.sh
├── deploy/             # سكربتات النشر
│   ├── deploy-staging.sh
│   ├── deploy-production.sh
│   └── rollback.sh
├── database/           # سكربتات قاعدة البيانات
│   ├── migrate.sh
│   ├── backup.sh
│   ├── restore.sh
│   └── migrations/
├── testing/            # سكربتات الاختبار
│   ├── run-tests.sh
│   ├── e2e-tests.sh
│   └── performance-tests.sh
├── maintenance/        # سكربتات الصيانة
│   ├── cleanup.sh
│   ├── update-deps.sh
│   └── security-scan.sh
└── utils/              # أدوات مساعدة
    ├── generate-docs.sh
    ├── check-code-quality.sh
    └── create-module.sh
```

## سكربتات الإعداد الأولي

### init-project.sh
```bash
#!/bin/bash
# إعداد المشروع الأولي

echo "🚀 بدء إعداد مشروع إدارة الحافلات المدرسية..."

# التحقق من متطلبات النظام
check_requirements() {
    echo "📋 فحص المتطلبات..."
    
    # فحص Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js 18 أو أحدث"
        exit 1
    fi
    
    # فحص npm
    if ! command -v npm &> /dev/null; then
        echo "❌ npm غير مثبت"
        exit 1
    fi
    
    # فحص Git
    if ! command -v git &> /dev/null; then
        echo "❌ Git غير مثبت"
        exit 1
    fi
    
    echo "✅ جميع المتطلبات متوفرة"
}

# تثبيت التبعيات
install_dependencies() {
    echo "📦 تثبيت التبعيات..."
    
    # تثبيت تبعيات الواجهة الأمامية
    if [ -f "package.json" ]; then
        npm install
        echo "✅ تم تثبيت تبعيات الواجهة الأمامية"
    fi
    
    # تثبيت Supabase CLI
    npm install -g supabase
    echo "✅ تم تثبيت Supabase CLI"
}

# إعداد متغيرات البيئة
setup_environment() {
    echo "🔧 إعداد متغيرات البيئة..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "📝 تم إنشاء ملف .env من .env.example"
        echo "⚠️  يرجى تحديث متغيرات البيئة في ملف .env"
    else
        echo "✅ ملف .env موجود بالفعل"
    fi
}

# إعداد Git hooks
setup_git_hooks() {
    echo "🔗 إعداد Git hooks..."
    
    # إنشاء pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# تشغيل فحص الكود قبل الـ commit
npm run lint
npm run type-check
EOF
    
    chmod +x .git/hooks/pre-commit
    echo "✅ تم إعداد Git hooks"
}

# إعداد قاعدة البيانات
setup_database() {
    echo "🗄️ إعداد قاعدة البيانات..."
    
    # تسجيل الدخول إلى Supabase
    echo "يرجى تسجيل الدخول إلى Supabase..."
    supabase login
    
    # ربط المشروع
    echo "يرجى إدخال معرف مشروع Supabase:"
    read -r project_id
    supabase link --project-ref "$project_id"
    
    # تطبيق الـ migrations
    supabase db push
    
    echo "✅ تم إعداد قاعدة البيانات"
}

# تشغيل جميع خطوات الإعداد
main() {
    check_requirements
    install_dependencies
    setup_environment
    setup_git_hooks
    setup_database
    
    echo ""
    echo "🎉 تم إعداد المشروع بنجاح!"
    echo ""
    echo "الخطوات التالية:"
    echo "1. تحديث متغيرات البيئة في ملف .env"
    echo "2. تشغيل المشروع: npm run dev"
    echo "3. فتح المتصفح على: http://localhost:5173"
    echo ""
}

main "$@"
```

### setup-db.sql
```sql
-- إعداد قاعدة البيانات الأولي
-- Initial Database Setup

-- تفعيل امتدادات PostgreSQL المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- إنشاء schema للنظام
CREATE SCHEMA IF NOT EXISTS school_bus_system;

-- تعيين search_path
SET search_path TO school_bus_system, public;

-- إنشاء جدول المدارس (Tenants)
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(100) UNIQUE,
    subdomain VARCHAR(50) UNIQUE,
    logo_url TEXT,
    primary_color VARCHAR(7) DEFAULT '#3b82f6',
    secondary_color VARCHAR(7) DEFAULT '#14b8a6',
    accent_color VARCHAR(7) DEFAULT '#10b981',
    warning_color VARCHAR(7) DEFAULT '#f59e0b',
    error_color VARCHAR(7) DEFAULT '#ef4444',
    enabled_modules TEXT[] DEFAULT ARRAY['auth', 'users', 'students', 'buses'],
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الأدوار
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    description TEXT,
    role_scope VARCHAR(20) DEFAULT 'tenant' CHECK (role_scope IN ('global', 'tenant', 'assigned', 'children', 'personal')),
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول ربط الأدوار بالصلاحيات
CREATE TABLE IF NOT EXISTS role_permission_map (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    phone VARCHAR(20),
    profile_photo_url TEXT,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret VARCHAR(32),
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول جلسات المصادقة
CREATE TABLE IF NOT EXISTS auth_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    access_token VARCHAR(500) NOT NULL,
    refresh_token VARCHAR(500),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    is_revoked BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول سجل تسجيل الدخول
CREATE TABLE IF NOT EXISTS login_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    ip_address INET NOT NULL,
    user_agent TEXT,
    login_successful BOOLEAN NOT NULL,
    failure_reason VARCHAR(255),
    location_country VARCHAR(2),
    location_city VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_user_id ON auth_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_access_token ON auth_sessions(access_token);
CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);

-- تفعيل Row Level Security
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_logs ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات RLS أساسية
CREATE POLICY "Users can only see their tenant data" ON users
    FOR ALL USING (tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()));

CREATE POLICY "Auth sessions belong to user" ON auth_sessions
    FOR ALL USING (user_id = auth.uid());

-- إنشاء دالة لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء triggers لتحديث updated_at
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج البيانات الأساسية
INSERT INTO roles (name, name_ar, name_en, description, role_scope, is_system_role) VALUES
('system_admin', 'مدير النظام', 'System Admin', 'مدير النظام العام', 'global', true),
('school_admin', 'مدير المدرسة', 'School Admin', 'مدير المدرسة', 'tenant', false),
('driver', 'سائق', 'Driver', 'سائق الحافلة', 'assigned', false),
('parent', 'ولي أمر', 'Parent', 'ولي أمر الطالب', 'children', false),
('student', 'طالب', 'Student', 'طالب', 'personal', false),
('traffic_supervisor', 'مشرف حركة', 'Traffic Supervisor', 'مشرف حركة المرور', 'tenant', false)
ON CONFLICT (name) DO NOTHING;

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, name_ar, name_en, description, module, action, resource) VALUES
-- صلاحيات إدارة النظام
('SYSTEM_MANAGE_ALL', 'إدارة النظام الكاملة', 'Full System Management', 'إدارة كاملة للنظام', 'system', 'manage', 'all'),
('TENANTS_MANAGE', 'إدارة المدارس', 'Manage Schools', 'إدارة المدارس والمستأجرين', 'tenants', 'manage', 'tenants'),

-- صلاحيات إدارة المستخدمين
('USERS_VIEW', 'عرض المستخدمين', 'View Users', 'عرض قائمة المستخدمين', 'users', 'view', 'users'),
('USERS_CREATE', 'إنشاء مستخدمين', 'Create Users', 'إنشاء مستخدمين جدد', 'users', 'create', 'users'),
('USERS_UPDATE', 'تعديل المستخدمين', 'Update Users', 'تعديل بيانات المستخدمين', 'users', 'update', 'users'),
('USERS_DELETE', 'حذف المستخدمين', 'Delete Users', 'حذف المستخدمين', 'users', 'delete', 'users'),

-- صلاحيات إدارة الطلاب
('STUDENTS_VIEW', 'عرض الطلاب', 'View Students', 'عرض قائمة الطلاب', 'students', 'view', 'students'),
('STUDENTS_CREATE', 'إنشاء طلاب', 'Create Students', 'إنشاء طلاب جدد', 'students', 'create', 'students'),
('STUDENTS_UPDATE', 'تعديل الطلاب', 'Update Students', 'تعديل بيانات الطلاب', 'students', 'update', 'students'),
('STUDENTS_DELETE', 'حذف الطلاب', 'Delete Students', 'حذف الطلاب', 'students', 'delete', 'students')
ON CONFLICT (name) DO NOTHING;

COMMENT ON SCHEMA school_bus_system IS 'مخطط نظام إدارة الحافلات المدرسية';
COMMENT ON TABLE tenants IS 'جدول المدارس (المستأجرين)';
COMMENT ON TABLE users IS 'جدول المستخدمين';
COMMENT ON TABLE roles IS 'جدول الأدوار';
COMMENT ON TABLE permissions IS 'جدول الصلاحيات';
```

## سكربتات البناء

### build-frontend.sh
```bash
#!/bin/bash
# بناء الواجهة الأمامية

echo "🏗️ بناء الواجهة الأمامية..."

# التحقق من وجود ملف package.json
if [ ! -f "package.json" ]; then
    echo "❌ ملف package.json غير موجود"
    exit 1
fi

# تثبيت التبعيات
echo "📦 تثبيت التبعيات..."
npm ci

# تشغيل فحص الكود
echo "🔍 فحص جودة الكود..."
npm run lint
npm run type-check

# تشغيل الاختبارات
echo "🧪 تشغيل الاختبارات..."
npm run test

# بناء المشروع
echo "🔨 بناء المشروع..."
npm run build

# فحص حجم الملفات
echo "📊 تحليل حجم الملفات..."
npm run analyze

echo "✅ تم بناء الواجهة الأمامية بنجاح"
```

## سكربتات النشر

### deploy-production.sh
```bash
#!/bin/bash
# نشر الإنتاج

echo "🚀 بدء نشر الإنتاج..."

# التحقق من الفرع الحالي
current_branch=$(git branch --show-current)
if [ "$current_branch" != "main" ]; then
    echo "❌ يجب أن تكون في فرع main للنشر"
    exit 1
fi

# التحقق من وجود تغييرات غير محفوظة
if [ -n "$(git status --porcelain)" ]; then
    echo "❌ يوجد تغييرات غير محفوظة"
    exit 1
fi

# بناء المشروع
./scripts/build/build-frontend.sh

# نشر إلى Vercel/Netlify
echo "🌐 نشر الواجهة الأمامية..."
npm run deploy:production

# نشر Supabase Functions
echo "⚡ نشر Supabase Functions..."
supabase functions deploy

# تطبيق migrations
echo "🗄️ تطبيق migrations..."
supabase db push --linked

echo "✅ تم النشر بنجاح"
```

## أفضل الممارسات

1. **الأمان**: عدم تضمين كلمات مرور في السكربتات
2. **المرونة**: دعم متغيرات البيئة المختلفة
3. **التوثيق**: توثيق كل سكربت ووظيفته
4. **الاختبار**: اختبار السكربتات قبل الاستخدام
5. **النسخ الاحتياطية**: إنشاء نسخ احتياطية قبل التغييرات الكبيرة
