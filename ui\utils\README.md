# أدوات واجهة المستخدم (UI Utils)

هذا المجلد يحتوي على الأدوات والمساعدات المشتركة لواجهة المستخدم.

## هيكل الأدوات

```
ui/utils/
├── cn.ts             # دمج فئات CSS
├── formatters.ts     # تنسيق البيانات
├── validators.ts     # التحقق من البيانات
├── constants.ts      # الثوابت
├── helpers.ts        # دوال مساعدة
├── hooks/            # React Hooks مخصصة
├── types.ts          # أنواع البيانات
└── animations.ts     # الحركات والانتقالات
```

## دمج فئات CSS (cn)

```typescript
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * دمج فئات CSS مع Tailwind CSS
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// الاستخدام
const buttonClasses = cn(
  'px-4 py-2 rounded-md font-medium',
  variant === 'primary' && 'bg-blue-500 text-white',
  variant === 'secondary' && 'bg-gray-200 text-gray-900',
  disabled && 'opacity-50 cursor-not-allowed'
);
```

## تنسيق البيانات (Formatters)

```typescript
/**
 * تنسيق التاريخ والوقت
 */
export const formatters = {
  // تنسيق التاريخ
  date: (date: Date | string, locale: string = 'ar-SA') => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(dateObj);
  },

  // تنسيق الوقت
  time: (date: Date | string, locale: string = 'ar-SA') => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(dateObj);
  },

  // تنسيق التاريخ والوقت
  datetime: (date: Date | string, locale: string = 'ar-SA') => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(dateObj);
  },

  // تنسيق الأرقام
  number: (num: number, locale: string = 'ar-SA') => {
    return new Intl.NumberFormat(locale).format(num);
  },

  // تنسيق العملة
  currency: (amount: number, currency: string = 'SAR', locale: string = 'ar-SA') => {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  },

  // تنسيق النسبة المئوية
  percentage: (value: number, locale: string = 'ar-SA') => {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value / 100);
  },

  // تنسيق حجم الملف
  fileSize: (bytes: number, locale: string = 'ar-SA') => {
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const sizesEn = ['B', 'KB', 'MB', 'GB'];
    const sizeArray = locale.startsWith('ar') ? sizes : sizesEn;
    
    if (bytes === 0) return `0 ${sizeArray[0]}`;
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = (bytes / Math.pow(1024, i)).toFixed(1);
    
    return `${size} ${sizeArray[i]}`;
  },

  // تنسيق رقم الهاتف
  phone: (phone: string, countryCode: string = '+966') => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 9) {
      return `${countryCode} ${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5)}`;
    }
    return phone;
  },

  // تنسيق الوقت النسبي (منذ كم من الوقت)
  timeAgo: (date: Date | string, locale: string = 'ar-SA') => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    const intervals = {
      ar: [
        { label: 'سنة', seconds: 31536000 },
        { label: 'شهر', seconds: 2592000 },
        { label: 'أسبوع', seconds: 604800 },
        { label: 'يوم', seconds: 86400 },
        { label: 'ساعة', seconds: 3600 },
        { label: 'دقيقة', seconds: 60 },
        { label: 'ثانية', seconds: 1 },
      ],
      en: [
        { label: 'year', seconds: 31536000 },
        { label: 'month', seconds: 2592000 },
        { label: 'week', seconds: 604800 },
        { label: 'day', seconds: 86400 },
        { label: 'hour', seconds: 3600 },
        { label: 'minute', seconds: 60 },
        { label: 'second', seconds: 1 },
      ],
    };

    const intervalArray = locale.startsWith('ar') ? intervals.ar : intervals.en;
    const prefix = locale.startsWith('ar') ? 'منذ' : '';
    const suffix = locale.startsWith('ar') ? '' : 'ago';

    for (const interval of intervalArray) {
      const count = Math.floor(diffInSeconds / interval.seconds);
      if (count >= 1) {
        return `${prefix} ${count} ${interval.label} ${suffix}`.trim();
      }
    }

    return locale.startsWith('ar') ? 'الآن' : 'now';
  },
};
```

## التحقق من البيانات (Validators)

```typescript
/**
 * دوال التحقق من صحة البيانات
 */
export const validators = {
  // التحقق من البريد الإلكتروني
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // التحقق من رقم الهاتف السعودي
  saudiPhone: (phone: string): boolean => {
    const phoneRegex = /^(05|5)[0-9]{8}$/;
    const cleaned = phone.replace(/\D/g, '');
    return phoneRegex.test(cleaned);
  },

  // التحقق من قوة كلمة المرور
  password: (password: string): { isValid: boolean; score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('يجب أن تكون كلمة المرور 8 أحرف على الأقل');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('يجب أن تحتوي على حرف صغير');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('يجب أن تحتوي على حرف كبير');
    }

    if (/[0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.push('يجب أن تحتوي على رقم');
    }

    if (/[^a-zA-Z0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.push('يجب أن تحتوي على رمز خاص');
    }

    return {
      isValid: score >= 4,
      score,
      feedback,
    };
  },

  // التحقق من الرقم القومي السعودي
  saudiId: (id: string): boolean => {
    const idRegex = /^[12][0-9]{9}$/;
    if (!idRegex.test(id)) return false;

    // خوارزمية التحقق من صحة الرقم القومي
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      const digit = parseInt(id[i]);
      if (i % 2 === 0) {
        const doubled = digit * 2;
        sum += doubled > 9 ? doubled - 9 : doubled;
      } else {
        sum += digit;
      }
    }

    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit === parseInt(id[9]);
  },

  // التحقق من صيغة الملف
  fileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  // التحقق من حجم الملف
  fileSize: (file: File, maxSizeInMB: number): boolean => {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  },

  // التحقق من URL
  url: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // التحقق من الرمز البريدي السعودي
  saudiPostalCode: (code: string): boolean => {
    const postalCodeRegex = /^[0-9]{5}$/;
    return postalCodeRegex.test(code);
  },
};
```

## الثوابت (Constants)

```typescript
/**
 * الثوابت المستخدمة في واجهة المستخدم
 */
export const UI_CONSTANTS = {
  // أحجام الملفات
  MAX_FILE_SIZE: {
    IMAGE: 5 * 1024 * 1024, // 5MB
    DOCUMENT: 10 * 1024 * 1024, // 10MB
    VIDEO: 50 * 1024 * 1024, // 50MB
  },

  // أنواع الملفات المسموحة
  ALLOWED_FILE_TYPES: {
    IMAGE: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    DOCUMENT: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    EXCEL: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  },

  // أبعاد الشاشة
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
  },

  // مدة الانتقالات
  ANIMATION_DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },

  // أعداد الصفحات
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  },

  // مدة عرض الإشعارات
  TOAST_DURATION: {
    SUCCESS: 3000,
    ERROR: 5000,
    WARNING: 4000,
    INFO: 3000,
  },

  // حدود النصوص
  TEXT_LIMITS: {
    SHORT_TEXT: 100,
    MEDIUM_TEXT: 500,
    LONG_TEXT: 2000,
  },

  // أولويات الإشعارات
  NOTIFICATION_PRIORITY: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent',
  },

  // حالات التحميل
  LOADING_STATES: {
    IDLE: 'idle',
    LOADING: 'loading',
    SUCCESS: 'success',
    ERROR: 'error',
  },
};
```

## دوال مساعدة (Helpers)

```typescript
/**
 * دوال مساعدة عامة
 */
export const helpers = {
  // تأخير التنفيذ
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // إنشاء معرف فريد
  generateId: (): string => {
    return Math.random().toString(36).substr(2, 9);
  },

  // نسخ النص إلى الحافظة
  copyToClipboard: async (text: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch {
      return false;
    }
  },

  // تحويل الملف إلى Base64
  fileToBase64: (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  },

  // تنزيل ملف
  downloadFile: (data: Blob | string, filename: string, type: string = 'text/plain') => {
    const blob = typeof data === 'string' ? new Blob([data], { type }) : data;
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  },

  // تحويل RGB إلى Hex
  rgbToHex: (r: number, g: number, b: number): string => {
    return `#${[r, g, b].map(x => x.toString(16).padStart(2, '0')).join('')}`;
  },

  // تحويل Hex إلى RGB
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null;
  },

  // تحديد ما إذا كان اللون فاتح أم داكن
  isLightColor: (hex: string): boolean => {
    const rgb = helpers.hexToRgb(hex);
    if (!rgb) return false;
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128;
  },

  // تقصير النص
  truncateText: (text: string, maxLength: number, suffix: string = '...'): string => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength - suffix.length) + suffix;
  },

  // تحويل النص إلى slug
  slugify: (text: string): string => {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },

  // فحص ما إذا كان الجهاز محمول
  isMobile: (): boolean => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },

  // فحص ما إذا كان المتصفح يدعم ميزة معينة
  supportsFeature: (feature: string): boolean => {
    return feature in window;
  },
};
```

## أنواع البيانات (Types)

```typescript
/**
 * أنواع البيانات المشتركة لواجهة المستخدم
 */
export interface BaseProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ComponentVariants {
  variant?: 'primary' | 'secondary' | 'accent' | 'warning' | 'error' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

export interface FilterConfig {
  [key: string]: any;
}

export interface TableColumn<T = any> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
}

export interface FormFieldProps {
  label?: string;
  error?: string;
  required?: boolean;
  helpText?: string;
}

export interface ToastOptions {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}
```

## أفضل الممارسات

1. **إعادة الاستخدام**: كتابة دوال قابلة لإعادة الاستخدام
2. **الأداء**: تحسين الدوال للأداء العالي
3. **الأمان**: التحقق من صحة البيانات
4. **التوثيق**: توثيق جميع الدوال مع أمثلة
5. **الاختبار**: كتابة اختبارات شاملة للدوال
