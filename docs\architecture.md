# الهيكل المعماري للنظام
## System Architecture

## نظرة عامة

نظام إدارة الحافلات المدرسية مبني على معمارية SaaS متعددة المستأجرين (Multi-Tenant) مع تطبيق مبادئ Clean Architecture لضمان قابلية التوسع والصيانة.

## المبادئ المعمارية

### 1. Clean Architecture
```
┌─────────────────────────────────────────┐
│              Presentation Layer          │
│         (UI Components & Pages)         │
├─────────────────────────────────────────┤
│            Application Layer            │
│        (Use Cases & Services)           │
├─────────────────────────────────────────┤
│              Domain Layer               │
│       (Entities & Business Logic)      │
├─────────────────────────────────────────┤
│           Infrastructure Layer          │
│      (Database & External Services)    │
└─────────────────────────────────────────┘
```

### 2. Multi-Tenant Architecture
```mermaid
graph TB
    subgraph "Application Layer"
        A[Frontend App]
        B[API Gateway]
    end
    
    subgraph "Data Layer"
        C[Shared Database]
        D[Tenant 1 Data]
        E[Tenant 2 Data]
        F[Tenant N Data]
    end
    
    subgraph "Security Layer"
        G[Row Level Security]
        H[JWT Authentication]
        I[RBAC Authorization]
    end
    
    A --> B
    B --> G
    G --> C
    C --> D
    C --> E
    C --> F
    
    H --> G
    I --> G
```

## الطبقات المعمارية

### 1. طبقة العرض (Presentation Layer)
**المسؤولية:** واجهة المستخدم والتفاعل

**المكونات:**
- React Components
- Pages & Layouts
- State Management (Zustand/Context)
- UI Library (Custom + Radix UI)

**التقنيات:**
- React 18 + TypeScript
- Vite (Build Tool)
- TailwindCSS (Styling)
- Framer Motion (Animations)

### 2. طبقة التطبيق (Application Layer)
**المسؤولية:** منطق العمل وحالات الاستخدام

**المكونات:**
- Use Cases
- Services
- Hooks
- API Clients

**الأنماط المطبقة:**
- Repository Pattern
- Service Layer Pattern
- Command Query Separation

### 3. طبقة المجال (Domain Layer)
**المسؤولية:** منطق العمل الأساسي والكيانات

**المكونات:**
- Entities (Student, Bus, Route, etc.)
- Value Objects
- Domain Services
- Business Rules

**المبادئ:**
- Domain-Driven Design (DDD)
- Single Responsibility
- Dependency Inversion

### 4. طبقة البنية التحتية (Infrastructure Layer)
**المسؤولية:** التفاعل مع الخدمات الخارجية

**المكونات:**
- Database Access (Supabase)
- External APIs (Mapbox, Payment Gateways)
- File Storage
- Notification Services

## معمارية قاعدة البيانات

### نموذج Multi-Tenant
```sql
-- كل جدول يحتوي على tenant_id
CREATE TABLE students (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    -- باقي الحقول
    CONSTRAINT students_tenant_fk FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- Row Level Security
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

CREATE POLICY "tenant_isolation" ON students
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);
```

### عزل البيانات (Data Isolation)
1. **Row Level Security (RLS)**: عزل على مستوى الصف
2. **Tenant Context**: تمرير معرف المستأجر في كل طلب
3. **API Middleware**: فحص الصلاحيات قبل الوصول للبيانات

## معمارية الأمان

### 1. المصادقة (Authentication)
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth Service
    participant D as Database
    
    U->>F: Login Request
    F->>A: Validate Credentials
    A->>D: Check User Data
    D-->>A: User Info
    A-->>F: JWT Token
    F-->>U: Login Success
    
    Note over F: Store JWT in HttpOnly Cookie
    Note over A: Generate Refresh Token
```

### 2. التخويل (Authorization)
```typescript
// RBAC Implementation
interface Permission {
  module: string;
  action: 'create' | 'read' | 'update' | 'delete';
  resource: string;
  scope: 'global' | 'tenant' | 'assigned' | 'children' | 'personal';
}

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  scope: 'global' | 'tenant';
}

// Permission Check
const hasPermission = (
  userRoles: Role[],
  requiredPermission: Permission,
  context: { tenantId: string; userId: string }
): boolean => {
  return userRoles.some(role =>
    role.permissions.some(permission =>
      permission.module === requiredPermission.module &&
      permission.action === requiredPermission.action &&
      permission.resource === requiredPermission.resource &&
      checkScope(permission.scope, context)
    )
  );
};
```

## معمارية الوحدات (Modular Architecture)

### هيكل الوحدة
```
apps/[module]/
├── domain/
│   ├── entities/       # الكيانات الأساسية
│   ├── interfaces/     # واجهات التفاعل
│   └── types/          # أنواع البيانات
├── application/
│   ├── services/       # خدمات العمل
│   ├── usecases/       # حالات الاستخدام
│   └── validators/     # التحقق من البيانات
├── infrastructure/
│   ├── repositories/   # مستودعات البيانات
│   ├── external/       # الخدمات الخارجية
│   └── database/       # استعلامات قاعدة البيانات
├── ui/
│   ├── components/     # مكونات الواجهة
│   ├── pages/          # صفحات الوحدة
│   ├── hooks/          # React Hooks
│   └── styles/         # أنماط CSS
└── tests/              # اختبارات الوحدة
```

### تفعيل/تعطيل الوحدات
```typescript
// Module Registry
interface ModuleConfig {
  name: string;
  enabled: boolean;
  dependencies: string[];
  routes: RouteConfig[];
  permissions: Permission[];
}

class ModuleRegistry {
  private modules = new Map<string, ModuleConfig>();
  
  register(module: ModuleConfig) {
    this.modules.set(module.name, module);
  }
  
  isEnabled(moduleName: string, tenantId: string): boolean {
    const tenant = getTenant(tenantId);
    return tenant.enabledModules.includes(moduleName);
  }
  
  getEnabledModules(tenantId: string): ModuleConfig[] {
    const tenant = getTenant(tenantId);
    return Array.from(this.modules.values())
      .filter(module => tenant.enabledModules.includes(module.name));
  }
}
```

## معمارية الأداء

### 1. التخزين المؤقت (Caching)
```typescript
// Multi-Level Caching Strategy
interface CacheStrategy {
  // Browser Cache
  browserCache: {
    staticAssets: '1y';
    apiResponses: '5m';
  };
  
  // CDN Cache
  cdnCache: {
    images: '30d';
    fonts: '1y';
    scripts: '1y';
  };
  
  // Application Cache
  appCache: {
    userSessions: '24h';
    tenantConfig: '1h';
    staticData: '30m';
  };
  
  // Database Cache
  dbCache: {
    queryResults: '15m';
    aggregations: '5m';
  };
}
```

### 2. تحسين الاستعلامات
```sql
-- Optimized Queries with Proper Indexing
CREATE INDEX CONCURRENTLY idx_students_tenant_id ON students(tenant_id);
CREATE INDEX CONCURRENTLY idx_attendance_student_date ON attendance(student_id, date);
CREATE INDEX CONCURRENTLY idx_buses_location ON buses USING GIST(current_location);

-- Materialized Views for Heavy Aggregations
CREATE MATERIALIZED VIEW student_attendance_summary AS
SELECT 
    s.tenant_id,
    s.id as student_id,
    COUNT(a.id) as total_days,
    COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_days,
    ROUND(
        COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / COUNT(a.id), 
        2
    ) as attendance_rate
FROM students s
LEFT JOIN attendance a ON s.id = a.student_id
GROUP BY s.tenant_id, s.id;

-- Refresh strategy
REFRESH MATERIALIZED VIEW CONCURRENTLY student_attendance_summary;
```

## معمارية التوسع (Scalability Architecture)

### 1. التوسع الأفقي (Horizontal Scaling)
```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Nginx/Cloudflare]
    end
    
    subgraph "Application Servers"
        AS1[App Server 1]
        AS2[App Server 2]
        AS3[App Server N]
    end
    
    subgraph "Database Cluster"
        DB1[Primary DB]
        DB2[Read Replica 1]
        DB3[Read Replica 2]
    end
    
    subgraph "Cache Layer"
        R1[Redis Cluster]
    end
    
    LB --> AS1
    LB --> AS2
    LB --> AS3
    
    AS1 --> DB1
    AS1 --> DB2
    AS1 --> R1
    
    AS2 --> DB1
    AS2 --> DB3
    AS2 --> R1
    
    AS3 --> DB1
    AS3 --> DB2
    AS3 --> R1
    
    DB1 --> DB2
    DB1 --> DB3
```

### 2. استراتيجية التقسيم (Sharding Strategy)
```typescript
// Tenant-based Sharding
interface ShardConfig {
  shardId: string;
  databaseUrl: string;
  tenantIds: string[];
  capacity: number;
}

class ShardManager {
  private shards: ShardConfig[] = [];
  
  getShardForTenant(tenantId: string): ShardConfig {
    return this.shards.find(shard => 
      shard.tenantIds.includes(tenantId)
    ) || this.getDefaultShard();
  }
  
  redistributeTenants() {
    // Logic to balance tenants across shards
    // based on usage metrics and capacity
  }
}
```

## معمارية الأمان المتقدم

### 1. Defense in Depth
```typescript
// Security Layers
const securityLayers = {
  // Network Security
  network: {
    firewall: 'CloudFlare WAF',
    ddosProtection: 'CloudFlare DDoS',
    rateLimiting: 'Application Level'
  },
  
  // Application Security
  application: {
    authentication: 'JWT + 2FA',
    authorization: 'RBAC',
    inputValidation: 'Zod Schemas',
    outputEncoding: 'Automatic'
  },
  
  // Data Security
  data: {
    encryption: 'AES-256 at rest',
    transmission: 'TLS 1.3',
    backup: 'Encrypted backups',
    rls: 'Row Level Security'
  },
  
  // Infrastructure Security
  infrastructure: {
    secrets: 'Environment Variables',
    monitoring: 'Audit Logs',
    updates: 'Automated Security Patches'
  }
};
```

### 2. Zero Trust Architecture
```typescript
// Every request is verified
const zeroTrustMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  // 1. Verify JWT token
  const token = extractToken(req);
  const payload = await verifyJWT(token);
  
  // 2. Check user status
  const user = await getUserById(payload.userId);
  if (!user.isActive) throw new UnauthorizedError();
  
  // 3. Verify tenant access
  const tenantId = extractTenantId(req);
  if (user.tenantId !== tenantId) throw new ForbiddenError();
  
  // 4. Check permissions
  const requiredPermission = getRequiredPermission(req.route);
  if (!hasPermission(user.roles, requiredPermission)) {
    throw new ForbiddenError();
  }
  
  // 5. Rate limiting
  await checkRateLimit(user.id, req.ip);
  
  // 6. Audit logging
  await logRequest(user.id, req.method, req.path);
  
  next();
};
```

## معمارية المراقبة والتشخيص

### 1. Observability Stack
```typescript
// Monitoring Architecture
const observabilityStack = {
  // Metrics
  metrics: {
    application: 'Custom metrics + Prometheus',
    infrastructure: 'System metrics',
    business: 'KPI dashboards'
  },
  
  // Logging
  logging: {
    application: 'Structured JSON logs',
    access: 'Request/Response logs',
    audit: 'Security event logs',
    error: 'Error tracking with Sentry'
  },
  
  // Tracing
  tracing: {
    distributed: 'OpenTelemetry',
    database: 'Query performance',
    external: 'API call tracing'
  },
  
  // Alerting
  alerting: {
    uptime: 'Health check monitoring',
    performance: 'Response time alerts',
    errors: 'Error rate thresholds',
    security: 'Suspicious activity alerts'
  }
};
```

### 2. Health Checks
```typescript
// Comprehensive Health Monitoring
interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  details?: any;
}

const healthChecks = {
  async database(): Promise<HealthCheck> {
    const start = Date.now();
    try {
      await supabase.from('health_check').select('1').limit(1);
      return {
        name: 'database',
        status: 'healthy',
        responseTime: Date.now() - start
      };
    } catch (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - start,
        details: error.message
      };
    }
  },
  
  async externalServices(): Promise<HealthCheck[]> {
    return Promise.all([
      checkMapboxAPI(),
      checkPaymentGateway(),
      checkSMSService(),
      checkEmailService()
    ]);
  }
};
```

## أفضل الممارسات المعمارية

### 1. مبادئ SOLID
- **Single Responsibility**: كل كلاس له مسؤولية واحدة
- **Open/Closed**: مفتوح للتوسيع، مغلق للتعديل
- **Liskov Substitution**: قابلية الاستبدال
- **Interface Segregation**: فصل الواجهات
- **Dependency Inversion**: عكس التبعيات

### 2. Domain-Driven Design
- **Bounded Contexts**: سياقات محددة لكل مجال
- **Aggregates**: تجميع الكيانات المترابطة
- **Value Objects**: كائنات القيم
- **Domain Events**: أحداث المجال

### 3. Event-Driven Architecture
```typescript
// Event System
interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  data: any;
  timestamp: Date;
  version: number;
}

class EventBus {
  private handlers = new Map<string, Function[]>();
  
  subscribe(eventType: string, handler: Function) {
    const handlers = this.handlers.get(eventType) || [];
    handlers.push(handler);
    this.handlers.set(eventType, handlers);
  }
  
  async publish(event: DomainEvent) {
    const handlers = this.handlers.get(event.type) || [];
    await Promise.all(handlers.map(handler => handler(event)));
  }
}

// Usage
eventBus.subscribe('StudentRegistered', async (event) => {
  await sendWelcomeEmail(event.data.studentId);
  await createDefaultSettings(event.data.studentId);
});
```

## الخلاصة

هذه المعمارية تضمن:
- **القابلية للتوسع**: دعم آلاف المدارس والمستخدمين
- **الأمان**: حماية متعددة الطبقات
- **الصيانة**: كود منظم وقابل للصيانة
- **الأداء**: تحسينات على جميع المستويات
- **المرونة**: إمكانية إضافة ميزات جديدة بسهولة
