-- Migration: 001_initial_setup.sql
-- Description: إعداد البنية الأساسية للنظام مع الامتدادات والـ Schema
-- Created: 2024-01-15
-- Author: School Bus Management System

-- تفعيل امتدادات PostgreSQL المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- إنشاء schema للنظام
CREATE SCHEMA IF NOT EXISTS school_bus_system;

-- تعيين search_path
SET search_path TO school_bus_system, public;

-- إنشاء دالة تحديث updated_at
CREATE OR REPLACE FUNCTION school_bus_system.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- دالة إعداد السياق للمستأجر
CREATE OR REPLACE FUNCTION school_bus_system.set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة الحصول على المستأجر الحالي
CREATE OR REPLACE FUNCTION school_bus_system.get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة حساب المسافة بين نقطتين
CREATE OR REPLACE FUNCTION school_bus_system.calculate_distance(
    point1 GEOGRAPHY,
    point2 GEOGRAPHY
)
RETURNS DECIMAL AS $$
BEGIN
    RETURN ST_Distance(point1, point2) / 1000; -- بالكيلومتر
END;
$$ LANGUAGE plpgsql;

-- دالة فحص ما إذا كانت النقطة داخل نطاق معين
CREATE OR REPLACE FUNCTION school_bus_system.is_within_radius(
    center_point GEOGRAPHY,
    check_point GEOGRAPHY,
    radius_meters INTEGER
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN ST_DWithin(center_point, check_point, radius_meters);
END;
$$ LANGUAGE plpgsql;

COMMENT ON SCHEMA school_bus_system IS 'مخطط نظام إدارة الحافلات المدرسية';
COMMENT ON FUNCTION school_bus_system.update_updated_at_column() IS 'دالة تحديث الطابع الزمني updated_at تلقائياً';
COMMENT ON FUNCTION school_bus_system.set_tenant_context(UUID) IS 'تعيين سياق المستأجر الحالي';
COMMENT ON FUNCTION school_bus_system.get_current_tenant_id() IS 'الحصول على معرف المستأجر الحالي';
COMMENT ON FUNCTION school_bus_system.calculate_distance(GEOGRAPHY, GEOGRAPHY) IS 'حساب المسافة بين نقطتين جغرافيتين بالكيلومتر';
COMMENT ON FUNCTION school_bus_system.is_within_radius(GEOGRAPHY, GEOGRAPHY, INTEGER) IS 'فحص ما إذا كانت النقطة داخل نطاق معين بالمتر';
