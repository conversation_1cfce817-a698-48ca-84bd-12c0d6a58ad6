# مكونات واجهة المستخدم (UI Components)

هذا المجلد يحتوي على جميع مكونات واجهة المستخدم القابلة لإعادة الاستخدام.

## هيكل المكونات

```
ui/components/
├── base/             # المكونات الأساسية
├── forms/            # مكونات النماذج
├── navigation/       # مكونات التنقل
├── data-display/     # مكونات عرض البيانات
├── feedback/         # مكونات التغذية الراجعة
├── layout/           # مكونات التخطيط
├── media/            # مكونات الوسائط
├── overlays/         # مكونات التراكب
└── specialized/      # مكونات متخصصة
```

## المكونات الأساسية (Base Components)

### Button
```tsx
// أنواع الأزرار المختلفة
<Button variant="primary">أساسي</Button>
<Button variant="secondary">ثانوي</Button>
<Button variant="accent">مميز</Button>
<Button variant="warning">تحذير</Button>
<Button variant="error">خطأ</Button>
<Button variant="ghost">شفاف</Button>
```

### Input
```tsx
// حقول الإدخال المختلفة
<Input type="text" placeholder="النص" />
<Input type="email" placeholder="البريد الإلكتروني" />
<Input type="password" placeholder="كلمة المرور" />
<Input type="number" placeholder="الرقم" />
<Input type="tel" placeholder="رقم الهاتف" />
```

### Typography
```tsx
// عناصر النصوص
<Heading level={1}>عنوان رئيسي</Heading>
<Heading level={2}>عنوان فرعي</Heading>
<Text size="large">نص كبير</Text>
<Text size="medium">نص متوسط</Text>
<Text size="small">نص صغير</Text>
```

## مكونات النماذج (Form Components)

### FormField
```tsx
<FormField label="الاسم" required error="هذا الحقل مطلوب">
  <Input type="text" />
</FormField>
```

### Select
```tsx
<Select placeholder="اختر خياراً">
  <SelectOption value="option1">الخيار الأول</SelectOption>
  <SelectOption value="option2">الخيار الثاني</SelectOption>
</Select>
```

### Checkbox & Radio
```tsx
<Checkbox label="موافق على الشروط" />
<RadioGroup name="gender">
  <Radio value="male" label="ذكر" />
  <Radio value="female" label="أنثى" />
</RadioGroup>
```

### DatePicker
```tsx
<DatePicker 
  label="تاريخ الميلاد"
  placeholder="اختر التاريخ"
  locale="ar"
/>
```

### FileUpload
```tsx
<FileUpload
  accept="image/*"
  maxSize={5 * 1024 * 1024} // 5MB
  multiple={false}
  onUpload={handleUpload}
/>
```

## مكونات التنقل (Navigation Components)

### Sidebar
```tsx
<Sidebar>
  <SidebarItem icon={HomeIcon} label="الرئيسية" href="/" />
  <SidebarItem icon={BusIcon} label="الحافلات" href="/buses" />
  <SidebarItem icon={UsersIcon} label="المستخدمين" href="/users" />
</Sidebar>
```

### Breadcrumb
```tsx
<Breadcrumb>
  <BreadcrumbItem href="/">الرئيسية</BreadcrumbItem>
  <BreadcrumbItem href="/buses">الحافلات</BreadcrumbItem>
  <BreadcrumbItem>تفاصيل الحافلة</BreadcrumbItem>
</Breadcrumb>
```

### Tabs
```tsx
<Tabs defaultValue="info">
  <TabsList>
    <TabsTrigger value="info">المعلومات</TabsTrigger>
    <TabsTrigger value="history">السجل</TabsTrigger>
  </TabsList>
  <TabsContent value="info">محتوى المعلومات</TabsContent>
  <TabsContent value="history">محتوى السجل</TabsContent>
</Tabs>
```

## مكونات عرض البيانات (Data Display)

### Table
```tsx
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>الاسم</TableHead>
      <TableHead>البريد الإلكتروني</TableHead>
      <TableHead>الإجراءات</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>أحمد محمد</TableCell>
      <TableCell><EMAIL></TableCell>
      <TableCell>
        <Button size="sm">تعديل</Button>
      </TableCell>
    </TableRow>
  </TableBody>
</Table>
```

### Card
```tsx
<Card>
  <CardHeader>
    <CardTitle>عنوان البطاقة</CardTitle>
    <CardDescription>وصف البطاقة</CardDescription>
  </CardHeader>
  <CardContent>
    محتوى البطاقة
  </CardContent>
  <CardFooter>
    <Button>إجراء</Button>
  </CardFooter>
</Card>
```

### Badge
```tsx
<Badge variant="success">نشط</Badge>
<Badge variant="warning">معلق</Badge>
<Badge variant="error">محذوف</Badge>
```

### Avatar
```tsx
<Avatar>
  <AvatarImage src="/avatar.jpg" alt="أحمد محمد" />
  <AvatarFallback>أم</AvatarFallback>
</Avatar>
```

## مكونات التغذية الراجعة (Feedback Components)

### Toast
```tsx
// استخدام Toast للإشعارات
toast.success("تم الحفظ بنجاح");
toast.error("حدث خطأ");
toast.warning("تحذير");
toast.info("معلومة");
```

### Alert
```tsx
<Alert variant="success">
  <AlertIcon />
  <AlertTitle>نجح!</AlertTitle>
  <AlertDescription>تم تنفيذ العملية بنجاح</AlertDescription>
</Alert>
```

### Loading
```tsx
<Loading size="small" />
<Loading size="medium" />
<Loading size="large" />
<LoadingSpinner />
<LoadingSkeleton />
```

### Progress
```tsx
<Progress value={75} max={100} />
<ProgressCircular value={50} />
```

## مكونات التراكب (Overlay Components)

### Modal
```tsx
<Modal open={isOpen} onClose={setIsOpen}>
  <ModalHeader>
    <ModalTitle>عنوان النافذة</ModalTitle>
  </ModalHeader>
  <ModalBody>
    محتوى النافذة
  </ModalBody>
  <ModalFooter>
    <Button onClick={setIsOpen}>إغلاق</Button>
  </ModalFooter>
</Modal>
```

### Dropdown
```tsx
<Dropdown>
  <DropdownTrigger>
    <Button>القائمة</Button>
  </DropdownTrigger>
  <DropdownContent>
    <DropdownItem>خيار 1</DropdownItem>
    <DropdownItem>خيار 2</DropdownItem>
  </DropdownContent>
</Dropdown>
```

### Tooltip
```tsx
<Tooltip content="نص التلميح">
  <Button>مرر الماوس هنا</Button>
</Tooltip>
```

## المكونات المتخصصة (Specialized Components)

### MapComponent
```tsx
<MapComponent
  center={[lat, lng]}
  zoom={12}
  markers={markers}
  onMarkerClick={handleMarkerClick}
/>
```

### QRCodeScanner
```tsx
<QRCodeScanner
  onScan={handleScan}
  onError={handleError}
/>
```

### Chart
```tsx
<Chart
  type="line"
  data={chartData}
  options={chartOptions}
/>
```

### Calendar
```tsx
<Calendar
  value={selectedDate}
  onChange={setSelectedDate}
  locale="ar"
/>
```

## الثيمات والتخصيص

### نظام الألوان
```css
:root {
  --color-primary: #3b82f6;
  --color-secondary: #14b8a6;
  --color-accent: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
}
```

### الوضع الداكن
```css
[data-theme="dark"] {
  --color-background: #1f2937;
  --color-foreground: #f9fafb;
}
```

### دعم RTL
```css
[dir="rtl"] {
  text-align: right;
}
```

## أفضل الممارسات

1. **إعادة الاستخدام**: تصميم مكونات قابلة لإعادة الاستخدام
2. **الوصولية**: دعم قارئات الشاشة والتنقل بلوحة المفاتيح
3. **الاستجابة**: تصميم متجاوب لجميع الأجهزة
4. **الأداء**: تحسين الأداء وتجنب إعادة الرسم غير الضرورية
5. **التوثيق**: توثيق جميع المكونات مع أمثلة الاستخدام
