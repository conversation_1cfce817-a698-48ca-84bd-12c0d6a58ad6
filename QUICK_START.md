# 🚀 دليل البدء السريع - نظام إدارة الحافلات المدرسية
## Quick Start Guide - School Bus Management System

## 📋 نظرة عامة

تم ترحيل **62.5%** من قاعدة البيانات بنجاح إلى Supabase. النظام جاهز للاستخدام والتطوير!

### ✅ ما هو جاهز:
- 🗄️ **11 جدول** مع بنية كاملة
- 🔒 **نظام أمان متقدم** مع RLS
- 🌍 **دعم PostGIS** للبيانات الجغرافية
- ⚡ **فهارس محسنة** للأداء العالي
- 🔧 **6 دوال مساعدة** للعمليات المتقدمة

## 🔧 الإعداد السريع

### 1. معلومات الاتصال
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://lfvnkfzlztjnwyluzoii.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database URL
DATABASE_URL=postgresql://postgres:[password]@db.lfvnkfzlztjnwyluzoii.supabase.co:5432/postgres
```

### 2. الحصول على مفاتيح API
1. اذهب إلى [إعدادات API](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii/settings/api)
2. انسخ `anon public` key
3. انسخ `service_role` key (للعمليات الإدارية فقط)

### 3. تثبيت المكتبات
```bash
# React + TypeScript + Vite
npm create vite@latest school-bus-app -- --template react-ts
cd school-bus-app

# Supabase Client
npm install @supabase/supabase-js

# UI Libraries (اختياري)
npm install @headlessui/react @heroicons/react
npm install tailwindcss @tailwindcss/forms
```

### 4. إعداد Supabase Client
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://lfvnkfzlztjnwyluzoii.supabase.co';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'school_bus_system'
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true
  }
});

// تعيين سياق المستأجر
export const setTenantContext = async (tenantId: string) => {
  const { error } = await supabase.rpc('set_tenant_context', {
    tenant_uuid: tenantId
  });
  
  if (error) throw error;
};
```

## 🏗️ البنية الأساسية

### الجداول المتاحة
```typescript
// أنواع البيانات الأساسية
interface Tenant {
  id: string;
  name: string;
  domain?: string;
  subdomain?: string;
  primary_color: string;
  secondary_color: string;
  enabled_modules: string[];
  settings: Record<string, any>;
  is_active: boolean;
}

interface User {
  id: string;
  email: string;
  name_ar: string;
  name_en?: string;
  phone?: string;
  tenant_id: string;
  role_id: string;
  is_active: boolean;
  email_verified: boolean;
  two_factor_enabled: boolean;
}

interface Student {
  id: string;
  name_ar: string;
  name_en?: string;
  grade?: string;
  class?: string;
  student_id?: string;
  parent_id?: string;
  photo_url?: string;
  tenant_id: string;
  is_active: boolean;
}

interface Bus {
  id: string;
  bus_number: string;
  plate_number: string;
  capacity: number;
  model?: string;
  year?: number;
  current_location?: any; // PostGIS Point
  driver_id?: string;
  tenant_id: string;
  status: 'available' | 'in_service' | 'maintenance' | 'out_of_service';
}
```

### العمليات الأساسية
```typescript
// جلب المدارس
const getTenants = async () => {
  const { data, error } = await supabase
    .from('tenants')
    .select('*')
    .eq('is_active', true);
  
  return { data, error };
};

// جلب المستخدمين (مع تعيين السياق)
const getUsers = async (tenantId: string) => {
  await setTenantContext(tenantId);
  
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      role:roles(name, name_ar)
    `)
    .eq('is_active', true);
  
  return { data, error };
};

// جلب الطلاب
const getStudents = async (tenantId: string) => {
  await setTenantContext(tenantId);
  
  const { data, error } = await supabase
    .from('students')
    .select(`
      *,
      parent:users(name_ar, phone)
    `)
    .eq('is_active', true);
  
  return { data, error };
};

// جلب الحافلات مع المواقع
const getBuses = async (tenantId: string) => {
  await setTenantContext(tenantId);
  
  const { data, error } = await supabase
    .from('buses')
    .select(`
      *,
      driver:drivers(name_ar, phone),
      routes(name, start_time, end_time)
    `)
    .eq('is_active', true);
  
  return { data, error };
};
```

## 🔐 نظام المصادقة

### تسجيل الدخول الأساسي
```typescript
// تسجيل دخول مع تعيين السياق
const signIn = async (email: string, password: string) => {
  // 1. تسجيل الدخول
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  if (authError) throw authError;
  
  // 2. جلب بيانات المستخدم
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select(`
      *,
      tenant:tenants(*),
      role:roles(*)
    `)
    .eq('id', authData.user.id)
    .single();
  
  if (userError) throw userError;
  
  // 3. تعيين سياق المستأجر
  await setTenantContext(userData.tenant_id);
  
  return { user: userData, session: authData.session };
};

// تسجيل الخروج
const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};
```

### فحص الصلاحيات
```typescript
const checkPermission = async (userId: string, permission: string) => {
  const { data, error } = await supabase
    .rpc('check_user_permission', {
      user_uuid: userId,
      permission_name: permission
    });
  
  return data;
};
```

## 🗺️ العمل مع البيانات الجغرافية

### إضافة موقع للحافلة
```typescript
const updateBusLocation = async (busId: string, lat: number, lng: number) => {
  const { error } = await supabase
    .from('buses')
    .update({
      current_location: `POINT(${lng} ${lat})`,
      last_location_update: new Date().toISOString()
    })
    .eq('id', busId);
  
  return { error };
};

// البحث عن الحافلات القريبة
const getNearbyBuses = async (lat: number, lng: number, radiusKm: number = 5) => {
  const { data, error } = await supabase
    .rpc('get_nearby_buses', {
      center_lat: lat,
      center_lng: lng,
      radius_km: radiusKm
    });
  
  return { data, error };
};
```

## 📊 التقارير والإحصائيات

### إحصائيات أساسية
```typescript
const getTenantStats = async (tenantId: string) => {
  await setTenantContext(tenantId);
  
  const [users, students, buses, routes] = await Promise.all([
    supabase.from('users').select('id', { count: 'exact' }),
    supabase.from('students').select('id', { count: 'exact' }),
    supabase.from('buses').select('id', { count: 'exact' }),
    supabase.from('routes').select('id', { count: 'exact' })
  ]);
  
  return {
    totalUsers: users.count || 0,
    totalStudents: students.count || 0,
    totalBuses: buses.count || 0,
    totalRoutes: routes.count || 0
  };
};

// حضور اليوم
const getTodayAttendance = async (tenantId: string) => {
  await setTenantContext(tenantId);
  
  const { data, error } = await supabase
    .from('attendance')
    .select('status')
    .eq('date', new Date().toISOString().split('T')[0]);
  
  const stats = data?.reduce((acc, record) => {
    acc[record.status] = (acc[record.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return { stats, error };
};
```

## 🧪 اختبار النظام

### فحص الاتصال
```typescript
const testConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('tenants')
      .select('count')
      .limit(1);
    
    if (error) throw error;
    
    console.log('✅ الاتصال بقاعدة البيانات ناجح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error);
    return false;
  }
};

// فحص صحة النظام (إذا كانت الدالة متاحة)
const healthCheck = async () => {
  try {
    const { data, error } = await supabase
      .rpc('database_health_check');
    
    console.log('فحص صحة النظام:', data);
    return data;
  } catch (error) {
    console.log('دالة فحص الصحة غير متاحة بعد');
    return null;
  }
};
```

## 🔄 إكمال الترحيل

### الملفات المتبقية
لإكمال الترحيل، نفذ الملفات التالية في [محرر SQL](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii/sql):

1. **006_seed_data.sql** - الأدوار والصلاحيات
2. **007_sample_data.sql** - البيانات التجريبية  
3. **008_views_and_functions.sql** - Views والدوال المتقدمة

### أو استخدم PowerShell
```powershell
# تعيين متغير البيئة
$env:SUPABASE_ACCESS_TOKEN = "your_token"

# تنفيذ السكربت
.\run_migration.ps1
```

## 🆘 استكشاف الأخطاء

### مشاكل شائعة
```typescript
// خطأ في الصلاحيات
if (error?.message?.includes('RLS')) {
  console.log('تأكد من تعيين سياق المستأجر');
  await setTenantContext(tenantId);
}

// خطأ في الاتصال
if (error?.message?.includes('connection')) {
  console.log('تحقق من مفاتيح API');
}

// خطأ في المصادقة
if (error?.message?.includes('auth')) {
  console.log('تحقق من تسجيل الدخول');
}
```

## 📚 موارد إضافية

- [لوحة التحكم](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii)
- [وثائق Supabase](https://supabase.com/docs)
- [مرجع PostGIS](https://postgis.net/docs/)
- [دليل React](https://react.dev/)

---

## 🎯 الخطوات التالية

1. **إكمال الترحيل** - تنفيذ الملفات المتبقية
2. **بناء واجهة المستخدم** - استخدام React + TypeScript
3. **تطبيق المصادقة** - نظام تسجيل دخول آمن
4. **إضافة الميزات** - حسب احتياجات المشروع

**النظام جاهز للتطوير الآن! 🚀**
