# مكونات التخطيط (Layout Components)

هذا المجلد يحتوي على مكونات التخطيط الأساسية للنظام.

## المكونات الرئيسية

### AppLayout
المكون الرئيسي لتخطيط التطبيق

```tsx
<AppLayout>
  <Header />
  <Sidebar />
  <MainContent>
    {children}
  </MainContent>
  <Footer />
</AppLayout>
```

### Header
شريط العلوي للتطبيق

**المميزات:**
- شعار النظام
- قائمة التنقل الرئيسية
- إشعارات المستخدم
- قائمة المستخدم
- تبديل اللغة
- تبديل الوضع الداكن

```tsx
<Header>
  <HeaderLogo />
  <HeaderNavigation />
  <HeaderActions>
    <NotificationBell />
    <LanguageSwitcher />
    <ThemeToggle />
    <UserMenu />
  </HeaderActions>
</Header>
```

### Sidebar
الشريط الجانبي للتنقل

**المميزات:**
- قائمة التنقل الرئيسية
- تجميع الروابط حسب الفئة
- إخفاء/إظهار حسب الصلاحيات
- دعم الطي والتوسيع
- دعم RTL/LTR

```tsx
<Sidebar collapsed={isCollapsed}>
  <SidebarHeader>
    <SidebarLogo />
    <SidebarToggle />
  </SidebarHeader>
  <SidebarContent>
    <SidebarSection title="الإدارة">
      <SidebarItem icon={UsersIcon} href="/users">المستخدمين</SidebarItem>
      <SidebarItem icon={BusIcon} href="/buses">الحافلات</SidebarItem>
    </SidebarSection>
  </SidebarContent>
</Sidebar>
```

### MainContent
المنطقة الرئيسية للمحتوى

```tsx
<MainContent>
  <ContentHeader>
    <PageTitle>عنوان الصفحة</PageTitle>
    <PageActions>
      <Button>إجراء جديد</Button>
    </PageActions>
  </ContentHeader>
  <ContentBody>
    {children}
  </ContentBody>
</MainContent>
```

### Footer
التذييل السفلي

```tsx
<Footer>
  <FooterContent>
    <FooterSection>
      <FooterTitle>روابط سريعة</FooterTitle>
      <FooterLink href="/help">المساعدة</FooterLink>
      <FooterLink href="/contact">اتصل بنا</FooterLink>
    </FooterSection>
    <FooterSection>
      <FooterCopyright>
        © 2024 نظام إدارة الحافلات المدرسية
      </FooterCopyright>
    </FooterSection>
  </FooterContent>
</Footer>
```

## مكونات التخطيط المساعدة

### Container
حاوي للمحتوى مع عرض محدود

```tsx
<Container size="sm">محتوى صغير</Container>
<Container size="md">محتوى متوسط</Container>
<Container size="lg">محتوى كبير</Container>
<Container size="xl">محتوى كبير جداً</Container>
<Container size="full">عرض كامل</Container>
```

### Grid
نظام الشبكة للتخطيط

```tsx
<Grid cols={12}>
  <GridItem span={6}>عمود نصف العرض</GridItem>
  <GridItem span={6}>عمود نصف العرض</GridItem>
</Grid>

<Grid cols={3} gap={4}>
  <GridItem>عمود 1</GridItem>
  <GridItem>عمود 2</GridItem>
  <GridItem>عمود 3</GridItem>
</Grid>
```

### Flex
تخطيط مرن

```tsx
<Flex direction="row" justify="between" align="center">
  <FlexItem>عنصر 1</FlexItem>
  <FlexItem>عنصر 2</FlexItem>
</Flex>

<Flex direction="column" gap={4}>
  <FlexItem>عنصر علوي</FlexItem>
  <FlexItem>عنصر سفلي</FlexItem>
</Flex>
```

### Stack
تكديس العناصر

```tsx
<VStack spacing={4}>
  <div>عنصر 1</div>
  <div>عنصر 2</div>
  <div>عنصر 3</div>
</VStack>

<HStack spacing={2}>
  <Button>زر 1</Button>
  <Button>زر 2</Button>
</HStack>
```

### Spacer
مساحة فارغة

```tsx
<Flex>
  <div>يسار</div>
  <Spacer />
  <div>يمين</div>
</Flex>
```

## التخطيطات المتخصصة

### DashboardLayout
تخطيط خاص بلوحات التحكم

```tsx
<DashboardLayout>
  <DashboardHeader>
    <DashboardTitle>لوحة التحكم</DashboardTitle>
    <DashboardFilters />
  </DashboardHeader>
  <DashboardGrid>
    <DashboardWidget span={2}>
      <MetricCard />
    </DashboardWidget>
    <DashboardWidget span={1}>
      <ChartWidget />
    </DashboardWidget>
  </DashboardGrid>
</DashboardLayout>
```

### FormLayout
تخطيط خاص بالنماذج

```tsx
<FormLayout>
  <FormSection title="المعلومات الأساسية">
    <FormGrid cols={2}>
      <FormField label="الاسم الأول">
        <Input />
      </FormField>
      <FormField label="الاسم الأخير">
        <Input />
      </FormField>
    </FormGrid>
  </FormSection>
  <FormActions>
    <Button type="submit">حفظ</Button>
    <Button variant="ghost">إلغاء</Button>
  </FormActions>
</FormLayout>
```

### TableLayout
تخطيط خاص بالجداول

```tsx
<TableLayout>
  <TableHeader>
    <TableTitle>قائمة المستخدمين</TableTitle>
    <TableActions>
      <TableSearch />
      <TableFilters />
      <Button>إضافة جديد</Button>
    </TableActions>
  </TableHeader>
  <TableContent>
    <Table />
  </TableContent>
  <TableFooter>
    <Pagination />
  </TableFooter>
</TableLayout>
```

## الاستجابة (Responsive Design)

### نقاط التوقف (Breakpoints)
```css
/* Mobile First Approach */
.container {
  width: 100%;
}

/* Tablet */
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

/* Large Desktop */
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
```

### تخطيط متجاوب
```tsx
<ResponsiveLayout>
  <MobileLayout>
    {/* تخطيط الهاتف */}
  </MobileLayout>
  <TabletLayout>
    {/* تخطيط الجهاز اللوحي */}
  </TabletLayout>
  <DesktopLayout>
    {/* تخطيط سطح المكتب */}
  </DesktopLayout>
</ResponsiveLayout>
```

## دعم RTL/LTR

### تبديل الاتجاه
```tsx
<DirectionProvider direction="rtl">
  <AppLayout />
</DirectionProvider>
```

### أنماط RTL
```css
[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

[dir="ltr"] .sidebar {
  left: 0;
  right: auto;
}
```

## الوضع الداكن

### تبديل الثيم
```tsx
<ThemeProvider theme="dark">
  <AppLayout />
</ThemeProvider>
```

### أنماط الوضع الداكن
```css
[data-theme="dark"] {
  --bg-primary: #1f2937;
  --text-primary: #f9fafb;
}

[data-theme="light"] {
  --bg-primary: #ffffff;
  --text-primary: #111827;
}
```

## أفضل الممارسات

1. **Mobile First**: البدء بتصميم الهاتف ثم التوسع
2. **Semantic HTML**: استخدام عناصر HTML الدلالية
3. **Accessibility**: دعم قارئات الشاشة والتنقل بلوحة المفاتيح
4. **Performance**: تحسين الأداء وتجنب إعادة الرسم
5. **Consistency**: الحفاظ على التناسق في التصميم
