# هيكلة الاختبارات (Testing Structure)

هذا المجلد يحتوي على جميع أنواع الاختبارات للنظام.

## هيكل الاختبارات

```
test/
├── unit/               # اختبارات الوحدة
│   ├── components/     # اختبارات المكونات
│   ├── utils/          # اختبارات الأدوات
│   ├── hooks/          # اختبارات React Hooks
│   └── services/       # اختبارات الخدمات
├── integration/        # اختبارات التكامل
│   ├── api/            # اختبارات API
│   ├── database/       # اختبارات قاعدة البيانات
│   └── auth/           # اختبارات المصادقة
├── e2e/                # اختبارات شاملة
│   ├── user-flows/     # تدفقات المستخدم
│   ├── admin-flows/    # تدفقات المدير
│   └── driver-flows/   # تدفقات السائق
├── performance/        # اختبارات الأداء
│   ├── load-tests/     # اختبارات الحمولة
│   ├── stress-tests/   # اختبارات الضغط
│   └── benchmarks/     # المعايير المرجعية
├── security/           # اختبارات الأمان
│   ├── auth-tests/     # اختبارات المصادقة
│   ├── rls-tests/      # اختبارات RLS
│   └── penetration/    # اختبارات الاختراق
├── accessibility/      # اختبارات إمكانية الوصول
├── visual/             # اختبارات بصرية
├── fixtures/           # بيانات تجريبية
├── mocks/              # محاكيات
├── helpers/            # مساعدات الاختبار
└── config/             # إعدادات الاختبار
```

## إعداد بيئة الاختبار

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/test/config/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@ui/(.*)$': '<rootDir>/ui/$1',
    '^@apps/(.*)$': '<rootDir>/apps/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    'ui/**/*.{ts,tsx}',
    'apps/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '<rootDir>/test/**/*.test.{ts,tsx}',
    '<rootDir>/test/**/*.spec.{ts,tsx}',
  ],
};
```

### Testing Library Setup
```typescript
// test/config/setup.ts
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { server } from '../mocks/server';

// إعداد Testing Library
configure({ testIdAttribute: 'data-testid' });

// إعداد MSW
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// محاكاة APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// محاكاة Geolocation API
Object.defineProperty(navigator, 'geolocation', {
  value: {
    getCurrentPosition: jest.fn(),
    watchPosition: jest.fn(),
    clearWatch: jest.fn(),
  },
});

// محاكاة Clipboard API
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn(),
    readText: jest.fn(),
  },
});
```

## اختبارات الوحدة (Unit Tests)

### اختبار مكون Button
```typescript
// test/unit/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@ui/components/Button';

describe('Button Component', () => {
  it('renders button with text', () => {
    render(<Button>اضغط هنا</Button>);
    expect(screen.getByRole('button', { name: 'اضغط هنا' })).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>اضغط هنا</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies correct variant styles', () => {
    render(<Button variant="primary">زر أساسي</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-primary-500');
  });

  it('disables button when disabled prop is true', () => {
    render(<Button disabled>زر معطل</Button>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('opacity-50');
  });

  it('shows loading state', () => {
    render(<Button loading>جاري التحميل</Button>);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
```

### اختبار Hook مخصص
```typescript
// test/unit/hooks/useAuth.test.ts
import { renderHook, act } from '@testing-library/react';
import { useAuth } from '@/hooks/useAuth';
import { AuthProvider } from '@/contexts/AuthContext';

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
);

describe('useAuth Hook', () => {
  it('initializes with no user', () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('logs in user successfully', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    await act(async () => {
      await result.current.login('<EMAIL>', 'password');
    });

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user?.email).toBe('<EMAIL>');
  });

  it('handles login errors', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    await act(async () => {
      try {
        await result.current.login('<EMAIL>', 'wrong');
      } catch (error) {
        expect(error.message).toBe('Invalid credentials');
      }
    });

    expect(result.current.isAuthenticated).toBe(false);
  });
});
```

## اختبارات التكامل (Integration Tests)

### اختبار API
```typescript
// test/integration/api/auth.test.ts
import { supabase } from '@/lib/supabase';
import { createTestUser, cleanupTestData } from '../helpers/testHelpers';

describe('Authentication API', () => {
  afterEach(async () => {
    await cleanupTestData();
  });

  it('creates user account successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name_ar: 'اختبار',
      name_en: 'Test',
    };

    const { data, error } = await supabase.auth.signUp(userData);
    
    expect(error).toBeNull();
    expect(data.user?.email).toBe(userData.email);
  });

  it('prevents duplicate email registration', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
    };

    // إنشاء المستخدم الأول
    await supabase.auth.signUp(userData);

    // محاولة إنشاء مستخدم بنفس البريد
    const { error } = await supabase.auth.signUp(userData);
    
    expect(error?.message).toContain('already registered');
  });

  it('enforces password strength requirements', async () => {
    const userData = {
      email: '<EMAIL>',
      password: '123', // كلمة مرور ضعيفة
    };

    const { error } = await supabase.auth.signUp(userData);
    
    expect(error?.message).toContain('Password should be at least 6 characters');
  });
});
```

### اختبار RLS
```typescript
// test/integration/database/rls.test.ts
import { supabase } from '@/lib/supabase';
import { createTestTenant, createTestUser } from '../helpers/testHelpers';

describe('Row Level Security', () => {
  let tenant1: any, tenant2: any;
  let user1: any, user2: any;

  beforeEach(async () => {
    // إنشاء مستأجرين منفصلين
    tenant1 = await createTestTenant('مدرسة الاختبار 1');
    tenant2 = await createTestTenant('مدرسة الاختبار 2');

    // إنشاء مستخدمين لكل مستأجر
    user1 = await createTestUser(tenant1.id, '<EMAIL>');
    user2 = await createTestUser(tenant2.id, '<EMAIL>');
  });

  it('prevents cross-tenant data access', async () => {
    // تسجيل دخول المستخدم الأول
    await supabase.auth.signInWithPassword({
      email: user1.email,
      password: 'TestPassword123!',
    });

    // محاولة الوصول لبيانات المستأجر الثاني
    const { data, error } = await supabase
      .from('students')
      .select('*')
      .eq('tenant_id', tenant2.id);

    expect(data).toHaveLength(0); // لا يجب أن يرى أي بيانات
  });

  it('allows access to own tenant data', async () => {
    // تسجيل دخول المستخدم الأول
    await supabase.auth.signInWithPassword({
      email: user1.email,
      password: 'TestPassword123!',
    });

    // الوصول لبيانات نفس المستأجر
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('tenant_id', tenant1.id);

    expect(error).toBeNull();
    expect(data).toBeDefined();
  });
});
```

## اختبارات شاملة (E2E Tests)

### Cypress Configuration
```typescript
// cypress.config.ts
import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5173',
    supportFile: 'test/e2e/support/e2e.ts',
    specPattern: 'test/e2e/**/*.cy.{js,jsx,ts,tsx}',
    video: true,
    screenshot: true,
    viewportWidth: 1280,
    viewportHeight: 720,
  },
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
    supportFile: 'test/e2e/support/component.ts',
    specPattern: 'test/e2e/components/**/*.cy.{js,jsx,ts,tsx}',
  },
});
```

### اختبار تدفق تسجيل الدخول
```typescript
// test/e2e/user-flows/login.cy.ts
describe('Login Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('logs in successfully with valid credentials', () => {
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('TestPassword123!');
    cy.get('[data-testid="login-button"]').click();

    cy.url().should('include', '/dashboard');
    cy.get('[data-testid="user-menu"]').should('be.visible');
  });

  it('shows error with invalid credentials', () => {
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('wrongpassword');
    cy.get('[data-testid="login-button"]').click();

    cy.get('[data-testid="error-message"]')
      .should('be.visible')
      .and('contain', 'بيانات الدخول غير صحيحة');
  });

  it('validates required fields', () => {
    cy.get('[data-testid="login-button"]').click();

    cy.get('[data-testid="email-error"]')
      .should('be.visible')
      .and('contain', 'البريد الإلكتروني مطلوب');
    
    cy.get('[data-testid="password-error"]')
      .should('be.visible')
      .and('contain', 'كلمة المرور مطلوبة');
  });

  it('supports RTL layout for Arabic', () => {
    cy.get('[data-testid="language-switcher"]').click();
    cy.get('[data-testid="arabic-option"]').click();

    cy.get('html').should('have.attr', 'dir', 'rtl');
    cy.get('[data-testid="login-form"]').should('have.css', 'text-align', 'right');
  });
});
```

## اختبارات الأداء

### Load Testing مع k6
```javascript
// test/performance/load-tests/api-load.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // رفع تدريجي إلى 100 مستخدم
    { duration: '5m', target: 100 }, // البقاء عند 100 مستخدم
    { duration: '2m', target: 200 }, // رفع إلى 200 مستخدم
    { duration: '5m', target: 200 }, // البقاء عند 200 مستخدم
    { duration: '2m', target: 0 },   // تقليل تدريجي إلى 0
  ],
  thresholds: {
    http_req_duration: ['p(99)<1500'], // 99% من الطلبات أقل من 1.5 ثانية
    http_req_failed: ['rate<0.1'],     // أقل من 10% فشل
  },
};

export default function () {
  // اختبار تسجيل الدخول
  let loginResponse = http.post('http://localhost:3000/api/auth/login', {
    email: '<EMAIL>',
    password: 'TestPassword123!',
  });

  check(loginResponse, {
    'login successful': (r) => r.status === 200,
    'login response time OK': (r) => r.timings.duration < 1000,
  });

  if (loginResponse.status === 200) {
    let token = loginResponse.json('access_token');
    
    // اختبار جلب البيانات
    let dataResponse = http.get('http://localhost:3000/api/students', {
      headers: { Authorization: `Bearer ${token}` },
    });

    check(dataResponse, {
      'data fetch successful': (r) => r.status === 200,
      'data response time OK': (r) => r.timings.duration < 500,
    });
  }

  sleep(1);
}
```

## مساعدات الاختبار

### Test Helpers
```typescript
// test/helpers/testHelpers.ts
import { supabase } from '@/lib/supabase';

export async function createTestTenant(name: string) {
  const { data, error } = await supabase
    .from('tenants')
    .insert({
      name,
      domain: `${name.toLowerCase().replace(/\s+/g, '-')}.test.com`,
      subdomain: name.toLowerCase().replace(/\s+/g, '-'),
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function createTestUser(tenantId: string, email: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password: 'TestPassword123!',
    options: {
      data: {
        tenant_id: tenantId,
        name_ar: 'مستخدم تجريبي',
        name_en: 'Test User',
      },
    },
  });

  if (error) throw error;
  return data.user;
}

export async function cleanupTestData() {
  // حذف البيانات التجريبية
  await supabase.from('tenants').delete().like('domain', '%.test.com');
}

export function mockGeolocation() {
  const mockGeolocation = {
    getCurrentPosition: jest.fn(),
    watchPosition: jest.fn(),
    clearWatch: jest.fn(),
  };

  Object.defineProperty(navigator, 'geolocation', {
    value: mockGeolocation,
  });

  return mockGeolocation;
}

export function createMockFile(name: string, size: number, type: string) {
  const file = new File([''], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
}
```

## تشغيل الاختبارات

### Package.json Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:unit": "jest test/unit",
    "test:integration": "jest test/integration",
    "test:e2e": "cypress run",
    "test:e2e:open": "cypress open",
    "test:performance": "k6 run test/performance/load-tests/api-load.js",
    "test:all": "npm run test && npm run test:e2e && npm run test:performance"
  }
}
```

## أفضل الممارسات

1. **التغطية**: الهدف 80% تغطية للكود
2. **السرعة**: اختبارات سريعة ومستقلة
3. **الوضوح**: أسماء اختبارات واضحة ووصفية
4. **العزل**: كل اختبار مستقل عن الآخرين
5. **البيانات**: استخدام بيانات تجريبية منفصلة
