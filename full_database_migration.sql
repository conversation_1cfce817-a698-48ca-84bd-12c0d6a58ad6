-- ترحيل قاعدة البيانات الكاملة من الصفر
-- Full Database Migration from Scratch

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- إنشاء schema للنظام
DROP SCHEMA IF EXISTS school_bus_system CASCADE;
CREATE SCHEMA school_bus_system;

-- تعيين search_path
SET search_path TO school_bus_system, public;

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION school_bus_system.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- دالة إعداد السياق للمستأجر
CREATE OR REPLACE FUNCTION school_bus_system.set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة الحصول على المستأجر الحالي
CREATE OR REPLACE FUNCTION school_bus_system.get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- جدول المدارس (المستأجرين)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(100) UNIQUE,
    subdomain VARCHAR(50) UNIQUE,
    logo_url TEXT,
    primary_color VARCHAR(7) DEFAULT '#3b82f6',
    secondary_color VARCHAR(7) DEFAULT '#14b8a6',
    accent_color VARCHAR(7) DEFAULT '#10b981',
    enabled_modules TEXT[] DEFAULT ARRAY['auth', 'users', 'students', 'buses'],
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الأدوار
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    description TEXT,
    role_scope VARCHAR(20) DEFAULT 'tenant' 
        CHECK (role_scope IN ('global', 'tenant', 'assigned', 'children', 'personal')),
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الصلاحيات
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول ربط الأدوار بالصلاحيات
CREATE TABLE role_permission_map (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- جدول المستخدمين
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    phone VARCHAR(20),
    profile_photo_url TEXT,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret VARCHAR(32),
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الطلاب
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    grade VARCHAR(50),
    class VARCHAR(50),
    student_id VARCHAR(50),
    parent_id UUID REFERENCES users(id),
    photo_url TEXT,
    files JSONB DEFAULT '[]',
    date_of_birth DATE,
    address TEXT,
    medical_notes TEXT,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول السائقين
CREATE TABLE drivers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    license_number VARCHAR(100) NOT NULL,
    license_expiry_date DATE,
    license_type VARCHAR(50),
    photo_url TEXT,
    documents JSONB DEFAULT '[]',
    address TEXT,
    date_of_birth DATE,
    national_id VARCHAR(20),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relation VARCHAR(50),
    hire_date DATE,
    salary DECIMAL(10,2),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الحافلات
CREATE TABLE buses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bus_number VARCHAR(50) NOT NULL,
    plate_number VARCHAR(20) UNIQUE NOT NULL,
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    model VARCHAR(100),
    year INTEGER,
    manufacturer VARCHAR(100),
    current_location GEOGRAPHY(Point, 4326),
    last_location_update TIMESTAMP WITH TIME ZONE,
    driver_id UUID REFERENCES drivers(id),
    color VARCHAR(50),
    fuel_type VARCHAR(30),
    features JSONB DEFAULT '{}',
    documents JSONB DEFAULT '[]',
    insurance_expiry_date DATE,
    registration_expiry_date DATE,
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'in_service', 'maintenance', 'out_of_service')),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المسارات
CREATE TABLE routes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    route_type VARCHAR(20) DEFAULT 'regular' CHECK (route_type IN ('regular', 'express', 'special')),
    bus_id UUID REFERENCES buses(id),
    driver_id UUID REFERENCES drivers(id),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    working_days INTEGER[] DEFAULT ARRAY[1,2,3,4,5],
    estimated_duration INTEGER,
    distance_km DECIMAL(8,2),
    max_students INTEGER,
    route_path GEOGRAPHY(LineString, 4326),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول نقاط التوقف
CREATE TABLE stops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    route_id UUID NOT NULL REFERENCES routes(id) ON DELETE CASCADE,
    stop_order INTEGER NOT NULL,
    location GEOGRAPHY(Point, 4326) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description TEXT,
    landmarks TEXT,
    arrival_time_estimate TIME,
    departure_time_estimate TIME,
    stop_type VARCHAR(20) DEFAULT 'pickup' CHECK (stop_type IN ('pickup', 'dropoff', 'both')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(route_id, stop_order)
);

-- جدول الحضور والانصراف
CREATE TABLE attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    bus_id UUID NOT NULL REFERENCES buses(id),
    route_id UUID REFERENCES routes(id),
    stop_id UUID REFERENCES stops(id),
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) NOT NULL CHECK (status IN ('boarded', 'alighted', 'absent', 'late', 'early')),
    location GEOGRAPHY(Point, 4326),
    notes TEXT,
    recorded_by UUID REFERENCES users(id),
    method VARCHAR(20) DEFAULT 'manual' CHECK (method IN ('manual', 'qr_code', 'nfc', 'biometric')),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL,
    category VARCHAR(30) DEFAULT 'general' CHECK (category IN ('general', 'attendance', 'emergency', 'maintenance', 'payment')),
    title_ar VARCHAR(255) NOT NULL,
    title_en VARCHAR(255),
    message_ar TEXT NOT NULL,
    message_en TEXT,
    recipient_role VARCHAR(50),
    recipient_id UUID REFERENCES users(id),
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    action_url TEXT,
    priority VARCHAR(20) DEFAULT 'medium' 
        CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    delivery_methods TEXT[] DEFAULT ARRAY['app'],
    sent_at TIMESTAMP WITH TIME ZONE,
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس للأداء
CREATE INDEX idx_tenants_domain ON tenants(domain);
CREATE INDEX idx_tenants_active ON tenants(is_active);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_students_tenant_id ON students(tenant_id);
CREATE INDEX idx_students_parent_id ON students(parent_id);
CREATE INDEX idx_drivers_tenant_id ON drivers(tenant_id);
CREATE INDEX idx_drivers_license_number ON drivers(license_number);
CREATE INDEX idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX idx_buses_driver_id ON buses(driver_id);
CREATE INDEX idx_buses_plate ON buses(plate_number);
CREATE INDEX idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX idx_routes_bus_id ON routes(bus_id);
CREATE INDEX idx_stops_route_id ON stops(route_id);
CREATE INDEX idx_attendance_student_date ON attendance(student_id, date);
CREATE INDEX idx_attendance_tenant_date ON attendance(tenant_id, date);
CREATE INDEX idx_notifications_recipient ON notifications(recipient_id, is_read);

-- فهارس مكانية
CREATE INDEX idx_buses_location ON buses USING GIST(current_location);
CREATE INDEX idx_routes_path ON routes USING GIST(route_path);
CREATE INDEX idx_stops_location ON stops USING GIST(location);

-- إنشاء triggers لتحديث updated_at
CREATE TRIGGER update_tenants_updated_at
    BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at
    BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at
    BEFORE UPDATE ON drivers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_buses_updated_at
    BEFORE UPDATE ON buses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_routes_updated_at
    BEFORE UPDATE ON routes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تفعيل RLS على جميع الجداول
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- سياسات العزل بين المستأجرين
CREATE POLICY "users_tenant_isolation" ON users
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "students_tenant_isolation" ON students
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "drivers_tenant_isolation" ON drivers
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "buses_tenant_isolation" ON buses
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "routes_tenant_isolation" ON routes
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "stops_tenant_isolation" ON stops
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM routes r
            WHERE r.id = route_id
            AND (r.tenant_id = get_current_tenant_id() OR get_current_tenant_id() IS NULL)
        )
    );

CREATE POLICY "attendance_tenant_isolation" ON attendance
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "notifications_tenant_isolation" ON notifications
    FOR ALL USING (
        tenant_id = get_current_tenant_id() OR
        get_current_tenant_id() IS NULL
    );

CREATE POLICY "tenants_system_admin_only" ON tenants
    FOR ALL USING (
        get_current_tenant_id() IS NULL OR
        id = get_current_tenant_id()
    );

SELECT 'Part 2: Indexes and RLS policies created successfully' as result;
