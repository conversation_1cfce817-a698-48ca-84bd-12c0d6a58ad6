# نظام الثيمات (Themes System)

هذا المجلد يحتوي على نظام الثيمات الشامل للتطبيق.

## هيكل الثيمات

```
ui/themes/
├── tokens/           # رموز التصميم الأساسية
│   ├── colors.ts     # الألوان
│   ├── typography.ts # الخطوط والنصوص
│   ├── spacing.ts    # المسافات
│   ├── shadows.ts    # الظلال
│   └── borders.ts    # الحدود
├── components/       # أنماط المكونات
├── layouts/          # أنماط التخطيطات
├── utilities/        # فئات المساعدة
└── themes/           # الثيمات المختلفة
    ├── light.ts      # الثيم الفاتح
    ├── dark.ts       # الثيم الداكن
    └── custom.ts     # ثيمات مخصصة
```

## رموز التصميم (Design Tokens)

### الألوان (Colors)
```typescript
export const colors = {
  // الألوان الأساسية
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // اللون الأساسي
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  
  // الألوان الثانوية
  secondary: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6', // اللون الثانوي
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a',
  },
  
  // اللون المميز
  accent: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // اللون المميز
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
  },
  
  // ألوان التحذير
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // لون التحذير
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  // ألوان الخطأ
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // لون الخطأ
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // الألوان المحايدة
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  // ألوان النجاح
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  // ألوان المعلومات
  info: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  }
};
```

### الخطوط (Typography)
```typescript
export const typography = {
  // عائلات الخطوط
  fontFamily: {
    sans: ['Cairo', 'Inter', 'system-ui', 'sans-serif'],
    serif: ['Georgia', 'serif'],
    mono: ['Fira Code', 'monospace'],
  },
  
  // أحجام الخطوط
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
  },
  
  // أوزان الخطوط
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
  
  // ارتفاع الأسطر
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
  
  // تباعد الأحرف
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  }
};
```

### المسافات (Spacing)
```typescript
export const spacing = {
  0: '0px',
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  7: '1.75rem',   // 28px
  8: '2rem',      // 32px
  9: '2.25rem',   // 36px
  10: '2.5rem',   // 40px
  11: '2.75rem',  // 44px
  12: '3rem',     // 48px
  14: '3.5rem',   // 56px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
  24: '6rem',     // 96px
  28: '7rem',     // 112px
  32: '8rem',     // 128px
  36: '9rem',     // 144px
  40: '10rem',    // 160px
  44: '11rem',    // 176px
  48: '12rem',    // 192px
  52: '13rem',    // 208px
  56: '14rem',    // 224px
  60: '15rem',    // 240px
  64: '16rem',    // 256px
  72: '18rem',    // 288px
  80: '20rem',    // 320px
  96: '24rem',    // 384px
};
```

### الظلال (Shadows)
```typescript
export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
};
```

### الحدود (Borders)
```typescript
export const borders = {
  // عرض الحدود
  borderWidth: {
    0: '0px',
    1: '1px',
    2: '2px',
    4: '4px',
    8: '8px',
  },
  
  // نصف قطر الحدود
  borderRadius: {
    none: '0px',
    sm: '0.125rem',   // 2px
    base: '0.25rem',  // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px',
  },
};
```

## الثيمات

### الثيم الفاتح (Light Theme)
```typescript
export const lightTheme = {
  colors: {
    background: {
      primary: colors.gray[50],
      secondary: colors.gray[100],
      tertiary: colors.white,
    },
    text: {
      primary: colors.gray[900],
      secondary: colors.gray[600],
      tertiary: colors.gray[400],
    },
    border: {
      primary: colors.gray[200],
      secondary: colors.gray[300],
    },
  },
};
```

### الثيم الداكن (Dark Theme)
```typescript
export const darkTheme = {
  colors: {
    background: {
      primary: colors.gray[900],
      secondary: colors.gray[800],
      tertiary: colors.gray[700],
    },
    text: {
      primary: colors.gray[50],
      secondary: colors.gray[300],
      tertiary: colors.gray[400],
    },
    border: {
      primary: colors.gray[700],
      secondary: colors.gray[600],
    },
  },
};
```

## استخدام الثيمات

### ThemeProvider
```tsx
import { ThemeProvider } from './themes/ThemeProvider';
import { lightTheme, darkTheme } from './themes';

function App() {
  const [theme, setTheme] = useState('light');
  
  return (
    <ThemeProvider theme={theme === 'light' ? lightTheme : darkTheme}>
      <AppLayout />
    </ThemeProvider>
  );
}
```

### استخدام الألوان في المكونات
```tsx
import { useTheme } from './themes/useTheme';

function Button({ variant = 'primary', children }) {
  const theme = useTheme();
  
  const styles = {
    backgroundColor: theme.colors.primary[500],
    color: theme.colors.white,
    padding: theme.spacing[3],
    borderRadius: theme.borders.borderRadius.md,
  };
  
  return <button style={styles}>{children}</button>;
}
```

## التخصيص للمدارس

### ثيم مخصص لكل مدرسة
```typescript
export function createSchoolTheme(schoolColors: SchoolColors) {
  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: schoolColors.primary,
      secondary: schoolColors.secondary,
      accent: schoolColors.accent,
    },
  };
}
```

### تطبيق الثيم المخصص
```tsx
function SchoolApp({ schoolId }) {
  const schoolTheme = useSchoolTheme(schoolId);
  
  return (
    <ThemeProvider theme={schoolTheme}>
      <AppLayout />
    </ThemeProvider>
  );
}
```

## دعم RTL

### أنماط RTL
```css
[dir="rtl"] {
  --text-align: right;
  --margin-start: margin-right;
  --margin-end: margin-left;
  --padding-start: padding-right;
  --padding-end: padding-left;
}

[dir="ltr"] {
  --text-align: left;
  --margin-start: margin-left;
  --margin-end: margin-right;
  --padding-start: padding-left;
  --padding-end: padding-right;
}
```

## أفضل الممارسات

1. **التناسق**: استخدام رموز التصميم المحددة
2. **إمكانية الوصول**: ضمان تباين كافٍ للألوان
3. **الأداء**: تحسين تحميل الخطوط والأنماط
4. **المرونة**: دعم التخصيص لكل مدرسة
5. **الصيانة**: تنظيم الأنماط في ملفات منفصلة
