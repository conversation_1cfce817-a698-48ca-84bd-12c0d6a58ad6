# نظام إدارة الحافلات المدرسية
## School Bus Management System

نظام SaaS متعدد المستأجرين لإدارة الحافلات المدرسية مع تتبع GPS والحضور الذكي

### المميزات الرئيسية
- 🚌 إدارة شاملة للحافلات والسائقين
- 📍 تتبع مباشر بنظام GPS
- 👥 إدارة الطلاب وأولياء الأمور
- 📱 نظام حضور ذكي بـ QR Code
- 🔔 إشعارات فورية متعددة القنوات
- 📊 تقارير وإحصائيات تفصيلية
- 💳 نظام دفع متكامل
- 🌍 دعم كامل للعربية والإنجليزية (RTL/LTR)
- 🔒 أمان متقدم مع تشفير البيانات

### التقنيات المستخدمة
- **Frontend:** React + TypeScript + Vite + TailwindCSS
- **Backend:** Supabase (PostgreSQL + PostGIS)
- **Authentication:** Supabase Auth + 2FA
- **Maps:** Mapbox
- **Payments:** Paymob/Fawry
- **UI Components:** Radix UI + Lucide React
- **Charts:** Recharts
- **Fonts:** Cairo (Arabic Support)

### الهيكل المعماري
```
Multi-Tenant SaaS Architecture
├── Row Level Security (RLS)
├── Clean Architecture Pattern
├── Modular Plugin System
└── Real-time Updates
```

### متطلبات النظام
- Node.js 18+
- PostgreSQL 14+ with PostGIS
- Supabase Account
- Mapbox Account

## 📊 حالة المشروع

### ✅ المرحلة الأولى: البنية الأساسية (مكتملة)
- هيكل المشروع المعياري
- مكتبة UI شاملة (50+ مكون)
- قاعدة البيانات مع PostGIS
- نظام الأمان المتقدم
- الوثائق التفصيلية

### ✅ ترحيل قاعدة البيانات: 62.5% مكتمل
- **مشروع Supabase:** `lfvnkfzlztjnwyluzoii`
- **11 جدول** مع بنية كاملة ونظام RLS
- **دعم PostGIS** للبيانات الجغرافية
- **50+ فهرس** محسن للأداء العالي
- **6 دوال مساعدة** للعمليات المتقدمة

### 🔄 المرحلة التالية: إكمال الترحيل وبناء التطبيق
- إكمال البيانات الأساسية والتجريبية
- بناء واجهات المستخدم
- تطبيق نظام المصادقة
- إضافة ميزات التتبع والإشعارات

### 🚀 البدء السريع
```bash
# للمطورين: ابدأ فوراً مع قاعدة البيانات الجاهزة
# راجع QUICK_START.md للتفاصيل الكاملة

# معلومات الاتصال
SUPABASE_URL=https://lfvnkfzlztjnwyluzoii.supabase.co
PROJECT_ID=lfvnkfzlztjnwyluzoii
REGION=eu-central-1
```

### التثبيت والتشغيل
```bash
# استنساخ المشروع
git clone [repository-url]
cd schoolBus

# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env

# تشغيل المشروع
npm run dev
```

### المتغيرات البيئية المطلوبة
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_MAPBOX_TOKEN=your_mapbox_token
VITE_PAYMOB_API_KEY=your_paymob_key
```

### الأدوار والصلاحيات
- **System Admin:** إدارة شاملة للنظام
- **School Admin:** إدارة المدرسة والمستخدمين
- **Driver:** إدارة الرحلات والحضور
- **Parent:** متابعة الأبناء والإشعارات
- **Student:** عرض البيانات الشخصية
- **Traffic Supervisor:** مراقبة الحركة والطوارئ

### الوثائق
- [دليل الإعداد](docs/setup-guide.md)
- [الهيكل المعماري](docs/architecture.md)
- [قائمة الوحدات](docs/modules-list.md)
- [مخطط قاعدة البيانات](docs/database-schema.md)
- [سجل التغييرات](docs/changelog.md)

### 🗄️ وثائق قاعدة البيانات
- [دليل ترحيل Supabase](supabase/README.md)
- [إعدادات Supabase](supabase-config.md)
- [دليل البدء السريع](QUICK_START.md)
- [حالة الترحيل المكتملة](MIGRATION_COMPLETE.md)

### الدعم والمساهمة
للدعم الفني أو المساهمة في المشروع، يرجى مراجعة الوثائق أو فتح issue جديد.

### الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطوير هذا النظام وفقاً لأعلى معايير الأمان والأداء لخدمة المجتمع التعليمي**
