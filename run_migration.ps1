# PowerShell script to run Supabase migration
# تنفيذ ترحيل قاعدة البيانات إلى Supabase

$projectId = "lfvnkfzlztjnwyluzoii"
$apiUrl = "https://api.supabase.com/v1/projects/$projectId/database/query"

# قراءة محتوى ملف SQL
$sqlContent = Get-Content -Path "temp_migration.sql" -Raw -Encoding UTF8

# تحويل المحتوى إلى JSON
$body = @{
    query = $sqlContent
} | ConvertTo-Json -Depth 10

# إعداد Headers
$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $env:SUPABASE_ACCESS_TOKEN"
}

try {
    Write-Host "🚀 تنفيذ ترحيل قاعدة البيانات..." -ForegroundColor Blue
    
    # تنفيذ الطلب
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $body -Headers $headers
    
    Write-Host "✅ تم تنفيذ الترحيل بنجاح!" -ForegroundColor Green
    Write-Host "النتيجة: $($response.result)" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ خطأ في تنفيذ الترحيل:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "تفاصيل الخطأ: $responseBody" -ForegroundColor Red
    }
}

# تنظيف الملف المؤقت
Remove-Item -Path "temp_migration.sql" -Force -ErrorAction SilentlyContinue

Write-Host "انتهى تنفيذ السكربت." -ForegroundColor Cyan
