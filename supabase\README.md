# دليل ترحيل قاعدة البيانات إلى Supabase
## Supabase Database Migration Guide

## نظرة عامة

هذا الدليل يوضح كيفية ترحيل قاعدة البيانات الكاملة لنظام إدارة الحافلات المدرسية إلى Supabase.

## معلومات المشروع

- **معرف المشروع:** `lfvnkfzlztjnwyluzoii`
- **اسم المشروع:** SchoolBus
- **المنطقة:** eu-central-1
- **URL:** https://lfvnkfzlztjnwyluzoii.supabase.co

## المتطلبات الأساسية

### 1. تثبيت Supabase CLI
```bash
# باستخدام npm
npm install -g supabase

# أو باستخدام Homebrew (macOS/Linux)
brew install supabase/tap/supabase

# أو تحميل مباشر
# https://github.com/supabase/cli/releases
```

### 2. تثبيت PostgreSQL Client (اختياري)
```bash
# Windows
# تحميل من: https://www.postgresql.org/download/windows/

# macOS
brew install postgresql

# Ubuntu/Debian
sudo apt-get install postgresql-client
```

## خطوات الترحيل

### 1. تسجيل الدخول إلى Supabase
```bash
supabase login
```

### 2. ربط المشروع المحلي
```bash
supabase link --project-ref lfvnkfzlztjnwyluzoii
```

### 3. تنفيذ ملفات الترحيل
```bash
# رفع جميع ملفات الترحيل
supabase db push

# أو تنفيذ ملف واحد
supabase db push --include-all
```

### 4. التحقق من الترحيل
```bash
# فحص حالة قاعدة البيانات
supabase db diff

# تشغيل فحص الصحة
supabase db reset --linked
```

### 5. إنشاء أنواع TypeScript
```bash
# إنشاء ملف الأنواع
supabase gen types typescript --linked > src/types/supabase.ts
```

## ملفات الترحيل

### ترتيب التنفيذ
1. **001_initial_setup.sql** - الإعداد الأساسي والامتدادات
2. **002_core_tables.sql** - الجداول الأساسية (المستأجرين، المستخدمين، الأدوار)
3. **003_entity_tables.sql** - جداول الكيانات (الطلاب، السائقين، الحافلات)
4. **004_routes_and_stops.sql** - المسارات ونقاط التوقف والحضور
5. **005_rls_policies.sql** - سياسات Row Level Security
6. **006_seed_data.sql** - البيانات الأساسية والأدوار
7. **007_sample_data.sql** - البيانات التجريبية
8. **008_views_and_functions.sql** - Views والدوال المساعدة

### محتوى كل ملف

#### 001_initial_setup.sql
- تفعيل امتدادات PostgreSQL (uuid-ossp, postgis, pg_stat_statements)
- إنشاء schema أساسي
- الدوال المساعدة الأساسية

#### 002_core_tables.sql
- جدول المستأجرين (tenants)
- جدول الأدوار (roles)
- جدول الصلاحيات (permissions)
- جدول المستخدمين (users)
- جداول المصادقة

#### 003_entity_tables.sql
- جدول الطلاب (students)
- جدول السائقين (drivers)
- جدول الحافلات (buses) مع دعم PostGIS

#### 004_routes_and_stops.sql
- جدول المسارات (routes)
- جدول نقاط التوقف (stops)
- جدول الحضور (attendance)
- جدول الإشعارات (notifications)

#### 005_rls_policies.sql
- تفعيل RLS على جميع الجداول
- سياسات العزل بين المستأجرين
- سياسات خاصة بالأدوار

#### 006_seed_data.sql
- الأدوار الأساسية (6 أدوار)
- الصلاحيات الأساسية (22 صلاحية)
- ربط الأدوار بالصلاحيات
- مدرسة تجريبية

#### 007_sample_data.sql
- حافلة تجريبية
- مسار تجريبي مع نقاط توقف
- طلاب تجريبيين
- سجلات حضور تجريبية

#### 008_views_and_functions.sql
- 5 Views للتقارير والإحصائيات
- دوال مساعدة متقدمة
- دالة فحص صحة النظام

## إعداد البيئة المحلية

### 1. إنشاء ملف البيئة
```bash
cp .env.example .env
```

### 2. تعبئة متغيرات البيئة
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://lfvnkfzlztjnwyluzoii.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database URL
DATABASE_URL=postgresql://postgres:[password]@db.lfvnkfzlztjnwyluzoii.supabase.co:5432/postgres
```

### 3. الحصول على مفاتيح API
1. اذهب إلى [لوحة تحكم Supabase](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii)
2. انتقل إلى Settings > API
3. انسخ `anon public` key و `service_role` key

## التحقق من نجاح الترحيل

### 1. فحص الجداول
```sql
-- عرض جميع الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'school_bus_system'
ORDER BY table_name;
```

### 2. فحص البيانات التجريبية
```sql
-- فحص المدرسة التجريبية
SELECT * FROM school_bus_system.tenants;

-- فحص المستخدمين
SELECT u.name_ar, r.name_ar as role_name 
FROM school_bus_system.users u
JOIN school_bus_system.roles r ON u.role_id = r.id;
```

### 3. فحص صحة النظام
```sql
-- تشغيل فحص شامل
SELECT * FROM school_bus_system.database_health_check();
```

## استخدام النظام

### 1. إعداد Supabase Client
```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://lfvnkfzlztjnwyluzoii.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'school_bus_system'
  }
});
```

### 2. تعيين سياق المستأجر
```typescript
// تعيين سياق المدرسة
await supabase.rpc('set_tenant_context', {
  tenant_uuid: '550e8400-e29b-41d4-a716-************'
});
```

### 3. استعلام البيانات
```typescript
// جلب الطلاب
const { data: students } = await supabase
  .from('students')
  .select('*')
  .eq('is_active', true);

// جلب الحافلات
const { data: buses } = await supabase
  .from('buses')
  .select(`
    *,
    driver:drivers(name_ar),
    routes(name)
  `);
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في الاتصال
```bash
# التحقق من حالة المشروع
supabase projects list

# إعادة ربط المشروع
supabase link --project-ref lfvnkfzlztjnwyluzoii
```

#### 2. خطأ في الصلاحيات
```sql
-- التحقق من RLS
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'school_bus_system';
```

#### 3. خطأ في البيانات
```sql
-- إعادة تعيين البيانات
SELECT * FROM school_bus_system.database_health_check();
```

## الدعم والمساعدة

### الموارد المفيدة
- [وثائق Supabase](https://supabase.com/docs)
- [مرجع API](https://supabase.com/docs/reference/javascript)
- [أمثلة التطبيقات](https://github.com/supabase/supabase/tree/master/examples)

### روابط المشروع
- [لوحة التحكم](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii)
- [محرر SQL](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii/sql)
- [إعدادات API](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii/settings/api)
- [قاعدة البيانات](https://supabase.com/dashboard/project/lfvnkfzlztjnwyluzoii/database/tables)

---

✅ **قاعدة البيانات جاهزة للاستخدام مع Supabase!**
