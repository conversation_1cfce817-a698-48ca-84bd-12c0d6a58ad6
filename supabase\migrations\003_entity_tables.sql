-- Migration: 003_entity_tables.sql
-- Description: إنشاء جداول الكيانات الرئيسية (الطلاب، السائقين، الحافلات)
-- Created: 2024-01-15
-- Author: School Bus Management System

SET search_path TO school_bus_system, public;

-- إن<PERSON><PERSON><PERSON> جدول الطلاب
CREATE TABLE IF NOT EXISTS students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- الأسماء
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    
    -- معلومات أساسية
    grade VARCHAR(50),
    class VARCHAR(50),
    student_id VARCHAR(50), -- رقم الطالب في المدرسة
    
    -- ولي الأمر
    parent_id UUID REFERENCES users(id),
    
    -- الصورة والملفات
    photo_url TEXT,
    files JSONB DEFAULT '[]', -- مصفوفة روابط الملفات
    
    -- معلومات إضافية
    date_of_birth DATE,
    address TEXT,
    medical_notes TEXT,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    
    -- الانتماء
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- حالة النشاط
    is_active BOOLEAN DEFAULT true,
    
    -- طوابع زمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول السائقين
CREATE TABLE IF NOT EXISTS drivers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- معلومات شخصية
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    
    -- معلومات الرخصة
    license_number VARCHAR(100) NOT NULL,
    license_expiry_date DATE,
    license_type VARCHAR(50), -- نوع الرخصة
    
    -- الصورة والملفات
    photo_url TEXT,
    documents JSONB DEFAULT '[]', -- مصفوفة الوثائق
    
    -- معلومات إضافية
    address TEXT,
    date_of_birth DATE,
    national_id VARCHAR(20),
    
    -- جهة الاتصال الطارئة
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relation VARCHAR(50),
    
    -- معلومات العمل
    hire_date DATE,
    salary DECIMAL(10,2),
    
    -- الانتماء
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- حالة النشاط
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الحافلات
CREATE TABLE IF NOT EXISTS buses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- معلومات الحافلة
    bus_number VARCHAR(50) NOT NULL,
    plate_number VARCHAR(20) UNIQUE NOT NULL,
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    model VARCHAR(100),
    year INTEGER,
    manufacturer VARCHAR(100),
    
    -- الموقع الحالي (PostGIS)
    current_location GEOGRAPHY(Point, 4326),
    last_location_update TIMESTAMP WITH TIME ZONE,
    
    -- السائق المعين
    driver_id UUID REFERENCES drivers(id),
    
    -- معلومات إضافية
    color VARCHAR(50),
    fuel_type VARCHAR(30), -- بنزين، ديزل، هجين
    features JSONB DEFAULT '{}', -- مكيف، واي فاي، إلخ
    documents JSONB DEFAULT '[]', -- وثائق الحافلة
    
    -- معلومات التأمين والرخصة
    insurance_expiry_date DATE,
    registration_expiry_date DATE,
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    
    -- حالة الحافلة
    status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'in_service', 'maintenance', 'out_of_service')),
    
    -- الانتماء
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- حالة النشاط
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_students_tenant_id ON students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_students_parent_id ON students(parent_id);
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_students_grade_class ON students(grade, class);
CREATE INDEX IF NOT EXISTS idx_students_active ON students(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_drivers_tenant_id ON drivers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_drivers_license_number ON drivers(license_number);
CREATE INDEX IF NOT EXISTS idx_drivers_phone ON drivers(phone);
CREATE INDEX IF NOT EXISTS idx_drivers_active ON drivers(tenant_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_drivers_license_expiry ON drivers(license_expiry_date) WHERE license_expiry_date IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_driver_id ON buses(driver_id);
CREATE INDEX IF NOT EXISTS idx_buses_number ON buses(bus_number);
CREATE INDEX IF NOT EXISTS idx_buses_plate ON buses(plate_number);
CREATE INDEX IF NOT EXISTS idx_buses_status ON buses(status);

-- فهرس مكاني للموقع
CREATE INDEX IF NOT EXISTS idx_buses_location ON buses USING GIST(current_location);

-- إنشاء triggers لتحديث updated_at
CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at 
    BEFORE UPDATE ON drivers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_buses_updated_at 
    BEFORE UPDATE ON buses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إضافة التعليقات
COMMENT ON TABLE students IS 'جدول الطلاب';
COMMENT ON TABLE drivers IS 'جدول السائقين';
COMMENT ON TABLE buses IS 'جدول الحافلات مع دعم تتبع GPS';

COMMENT ON COLUMN buses.current_location IS 'الموقع الحالي للحافلة (PostGIS Point)';
COMMENT ON COLUMN buses.features IS 'ميزات الحافلة (JSON): مكيف، واي فاي، كاميرات، إلخ';
COMMENT ON COLUMN students.files IS 'ملفات الطالب (JSON): الصور، الوثائق، إلخ';
COMMENT ON COLUMN drivers.documents IS 'وثائق السائق (JSON): الرخصة، الهوية، إلخ';
