-- Migration: 006_seed_data.sql
-- Description: إدراج البيانات الأساسية (الأدوار، الصلاحيات، البيانات التجريبية)
-- Created: 2024-01-15
-- Author: School Bus Management System

SET search_path TO school_bus_system, public;

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, name_ar, name_en, description, role_scope, is_system_role) VALUES
('system_admin', 'مدير النظام', 'System Admin', 'مدير النظام العام', 'global', true),
('school_admin', 'مدير المدرسة', 'School Admin', 'مدير المدرسة', 'tenant', false),
('driver', 'سائق', 'Driver', 'سائق الحافلة', 'assigned', false),
('parent', 'ولي أمر', 'Parent', 'ولي أمر الطالب', 'children', false),
('student', 'طالب', 'Student', 'طالب', 'personal', false),
('traffic_supervisor', 'مشرف حركة', 'Traffic Supervisor', 'مشرف حركة المرور', 'tenant', false)
ON CONFLICT (name) DO NOTHING;

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, name_ar, name_en, description, module, action, resource) VALUES
-- صلاحيات إدارة النظام
('SYSTEM_MANAGE_ALL', 'إدارة النظام الكاملة', 'Full System Management', 'إدارة كاملة للنظام', 'system', 'manage', 'all'),
('TENANTS_MANAGE', 'إدارة المدارس', 'Manage Schools', 'إدارة المدارس والمستأجرين', 'tenants', 'manage', 'tenants'),

-- صلاحيات إدارة المستخدمين
('USERS_VIEW', 'عرض المستخدمين', 'View Users', 'عرض قائمة المستخدمين', 'users', 'view', 'users'),
('USERS_CREATE', 'إنشاء مستخدمين', 'Create Users', 'إنشاء مستخدمين جدد', 'users', 'create', 'users'),
('USERS_UPDATE', 'تعديل المستخدمين', 'Update Users', 'تعديل بيانات المستخدمين', 'users', 'update', 'users'),
('USERS_DELETE', 'حذف المستخدمين', 'Delete Users', 'حذف المستخدمين', 'users', 'delete', 'users'),

-- صلاحيات إدارة الطلاب
('STUDENTS_VIEW', 'عرض الطلاب', 'View Students', 'عرض قائمة الطلاب', 'students', 'view', 'students'),
('STUDENTS_CREATE', 'إنشاء طلاب', 'Create Students', 'إنشاء طلاب جدد', 'students', 'create', 'students'),
('STUDENTS_UPDATE', 'تعديل الطلاب', 'Update Students', 'تعديل بيانات الطلاب', 'students', 'update', 'students'),
('STUDENTS_DELETE', 'حذف الطلاب', 'Delete Students', 'حذف الطلاب', 'students', 'delete', 'students'),

-- صلاحيات إدارة الحافلات
('BUSES_VIEW', 'عرض الحافلات', 'View Buses', 'عرض قائمة الحافلات', 'buses', 'view', 'buses'),
('BUSES_CREATE', 'إنشاء حافلات', 'Create Buses', 'إنشاء حافلات جديدة', 'buses', 'create', 'buses'),
('BUSES_UPDATE', 'تعديل الحافلات', 'Update Buses', 'تعديل بيانات الحافلات', 'buses', 'update', 'buses'),
('BUSES_DELETE', 'حذف الحافلات', 'Delete Buses', 'حذف الحافلات', 'buses', 'delete', 'buses'),

-- صلاحيات إدارة المسارات
('ROUTES_VIEW', 'عرض المسارات', 'View Routes', 'عرض قائمة المسارات', 'routes', 'view', 'routes'),
('ROUTES_CREATE', 'إنشاء مسارات', 'Create Routes', 'إنشاء مسارات جديدة', 'routes', 'create', 'routes'),
('ROUTES_UPDATE', 'تعديل المسارات', 'Update Routes', 'تعديل بيانات المسارات', 'routes', 'update', 'routes'),
('ROUTES_DELETE', 'حذف المسارات', 'Delete Routes', 'حذف المسارات', 'routes', 'delete', 'routes'),

-- صلاحيات الحضور
('ATTENDANCE_VIEW', 'عرض الحضور', 'View Attendance', 'عرض سجلات الحضور', 'attendance', 'view', 'attendance'),
('ATTENDANCE_RECORD', 'تسجيل الحضور', 'Record Attendance', 'تسجيل حضور الطلاب', 'attendance', 'create', 'attendance'),

-- صلاحيات التقارير
('REPORTS_VIEW', 'عرض التقارير', 'View Reports', 'عرض التقارير والإحصائيات', 'reports', 'view', 'reports'),
('REPORTS_EXPORT', 'تصدير التقارير', 'Export Reports', 'تصدير التقارير', 'reports', 'export', 'reports')
ON CONFLICT (name) DO NOTHING;

-- ربط الأدوار بالصلاحيات

-- مدير النظام - جميع الصلاحيات
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'system_admin'),
    id
FROM permissions
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- مدير المدرسة - صلاحيات إدارية محدودة
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'school_admin'),
    id
FROM permissions
WHERE name IN (
    'USERS_VIEW', 'USERS_CREATE', 'USERS_UPDATE',
    'STUDENTS_VIEW', 'STUDENTS_CREATE', 'STUDENTS_UPDATE', 'STUDENTS_DELETE',
    'BUSES_VIEW', 'BUSES_CREATE', 'BUSES_UPDATE',
    'ROUTES_VIEW', 'ROUTES_CREATE', 'ROUTES_UPDATE', 'ROUTES_DELETE',
    'ATTENDANCE_VIEW', 'ATTENDANCE_RECORD',
    'REPORTS_VIEW', 'REPORTS_EXPORT'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- السائق - صلاحيات محدودة
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'driver'),
    id
FROM permissions
WHERE name IN (
    'STUDENTS_VIEW',
    'BUSES_VIEW',
    'ROUTES_VIEW',
    'ATTENDANCE_VIEW', 'ATTENDANCE_RECORD'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- ولي الأمر - صلاحيات عرض فقط
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'parent'),
    id
FROM permissions
WHERE name IN (
    'STUDENTS_VIEW',
    'ATTENDANCE_VIEW',
    'ROUTES_VIEW'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- الطالب - صلاحيات عرض شخصية
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'student'),
    id
FROM permissions
WHERE name IN (
    'ATTENDANCE_VIEW',
    'ROUTES_VIEW'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- مشرف الحركة - صلاحيات مراقبة
INSERT INTO role_permission_map (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'traffic_supervisor'),
    id
FROM permissions
WHERE name IN (
    'BUSES_VIEW',
    'ROUTES_VIEW',
    'ATTENDANCE_VIEW',
    'REPORTS_VIEW'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- إضافة مدرسة تجريبية
INSERT INTO tenants (
    id, name, domain, subdomain, 
    primary_color, secondary_color, accent_color,
    enabled_modules, settings
) VALUES (
    '550e8400-e29b-41d4-a716-446655440001',
    'مدرسة المستقبل النموذجية',
    'future-model-school.edu.sa',
    'future-school',
    '#1e40af', -- أزرق داكن
    '#059669', -- أخضر تركوازي
    '#dc2626', -- أحمر
    ARRAY['auth', 'users', 'students', 'parents', 'drivers', 'buses', 'routes', 'attendance', 'notifications'],
    '{
        "school_type": "public",
        "grade_levels": ["KG1", "KG2", "1", "2", "3", "4", "5", "6"],
        "working_hours": {"start": "07:00", "end": "14:00"},
        "contact": {
            "phone": "+966112345678",
            "email": "<EMAIL>",
            "address": "الرياض، المملكة العربية السعودية"
        }
    }'
) ON CONFLICT (id) DO NOTHING;

-- إضافة مستخدم مدير المدرسة
INSERT INTO users (
    id, email, name_ar, name_en, phone,
    tenant_id, role_id, is_active, email_verified
) VALUES (
    '550e8400-e29b-41d4-a716-446655440002',
    '<EMAIL>',
    'أحمد محمد العلي',
    'Ahmed Mohammed Al-Ali',
    '+966501234567',
    '550e8400-e29b-41d4-a716-446655440001',
    (SELECT id FROM roles WHERE name = 'school_admin'),
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- إضافة سائق تجريبي
INSERT INTO drivers (
    id, name_ar, name_en, phone, email,
    license_number, license_expiry_date, license_type,
    address, emergency_contact_name, emergency_contact_phone,
    tenant_id, hire_date
) VALUES (
    '550e8400-e29b-41d4-a716-446655440003',
    'محمد عبدالله الأحمد',
    'Mohammed Abdullah Al-Ahmad',
    '+966502345678',
    '<EMAIL>',
    '**********',
    '2025-12-31',
    'رخصة عامة',
    'الرياض، حي العليا',
    'فاطمة محمد',
    '+966503456789',
    '550e8400-e29b-41d4-a716-446655440001',
    '2024-01-01'
) ON CONFLICT (id) DO NOTHING;
