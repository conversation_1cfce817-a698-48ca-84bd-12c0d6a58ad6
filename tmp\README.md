# ملفات تجريبية مؤقتة (Temporary Files)

هذا المجلد مخصص للملفات التجريبية والمؤقتة أثناء التطوير.

## الغرض من المجلد

- **التجريب**: اختبار ميزات جديدة قبل التطبيق
- **النماذج الأولية**: إنشاء نماذج أولية سريعة
- **البيانات المؤقتة**: تخزين بيانات مؤقتة أثناء التطوير
- **الاختبارات**: ملفات اختبار مؤقتة
- **التوثيق المسودة**: مسودات الوثائق قبل النشر

## هيكل المجلد

```
tmp/
├── prototypes/         # النماذج الأولية
├── experiments/        # التجارب والاختبارات
├── drafts/            # المسودات
├── data/              # بيانات مؤقتة
├── logs/              # ملفات السجل المؤقتة
├── uploads/           # الملفات المرفوعة مؤقتاً
└── cache/             # ملفات التخزين المؤقت
```

## قواعد الاستخدام

### ✅ مسموح
- ملفات التجريب والاختبار
- النماذج الأولية للميزات الجديدة
- مسودات الكود قبل التطبيق
- بيانات اختبار مؤقتة
- ملفات السجل أثناء التطوير

### ❌ غير مسموح
- ملفات الإنتاج
- بيانات حساسة أو شخصية
- كلمات مرور أو مفاتيح API
- ملفات كبيرة الحجم (>10MB)
- ملفات نهائية جاهزة للنشر

## التنظيف التلقائي

يتم تنظيف هذا المجلد تلقائياً:
- **يومياً**: حذف الملفات الأقدم من 7 أيام
- **أسبوعياً**: حذف الملفات الأقدم من 30 يوم
- **شهرياً**: تنظيف شامل للمجلد

## أمثلة على الاستخدام

### نموذج أولي لمكون جديد
```typescript
// tmp/prototypes/NewComponent.tsx
import React from 'react';

// نموذج أولي لمكون جديد
export function NewComponent() {
  return (
    <div className="p-4 border rounded">
      <h2>مكون جديد قيد التطوير</h2>
      <p>هذا نموذج أولي للاختبار</p>
    </div>
  );
}
```

### تجربة API جديد
```typescript
// tmp/experiments/new-api-test.ts
// تجربة API جديد قبل التطبيق

async function testNewAPI() {
  try {
    const response = await fetch('/api/new-endpoint');
    const data = await response.json();
    console.log('نتيجة التجربة:', data);
  } catch (error) {
    console.error('خطأ في التجربة:', error);
  }
}

// testNewAPI();
```

### بيانات اختبار
```json
// tmp/data/test-students.json
{
  "students": [
    {
      "id": "test-1",
      "name_ar": "أحمد محمد",
      "name_en": "Ahmed Mohammed",
      "grade": "الصف الخامس",
      "bus_id": "bus-001"
    },
    {
      "id": "test-2", 
      "name_ar": "فاطمة علي",
      "name_en": "Fatima Ali",
      "grade": "الصف السادس",
      "bus_id": "bus-002"
    }
  ]
}
```

### مسودة وثيقة
```markdown
<!-- tmp/drafts/new-feature-docs.md -->
# ميزة جديدة - مسودة

## الوصف
وصف الميزة الجديدة...

## المتطلبات
- متطلب 1
- متطلب 2

## التطبيق
خطوات التطبيق...

---
ملاحظة: هذه مسودة قيد المراجعة
```

## سكربت التنظيف

```bash
#!/bin/bash
# tmp/cleanup.sh
# سكربت تنظيف الملفات المؤقتة

echo "🧹 بدء تنظيف الملفات المؤقتة..."

# حذف الملفات الأقدم من 7 أيام
find tmp/ -type f -mtime +7 -delete

# حذف المجلدات الفارغة
find tmp/ -type d -empty -delete

# تنظيف ملفات السجل
find tmp/logs/ -name "*.log" -mtime +3 -delete

# تنظيف ملفات التخزين المؤقت
rm -rf tmp/cache/*

echo "✅ تم تنظيف الملفات المؤقتة"
```

## .gitignore

```gitignore
# تجاهل جميع الملفات في tmp/ ما عدا README.md
tmp/*
!tmp/README.md
!tmp/.gitkeep

# تجاهل ملفات محددة
*.tmp
*.temp
*.cache
*.log
```

## تحذيرات مهمة

⚠️ **تحذير**: لا تضع ملفات مهمة في هذا المجلد
⚠️ **تحذير**: قد يتم حذف الملفات تلقائياً
⚠️ **تحذير**: لا تضع بيانات حساسة هنا
⚠️ **تحذير**: استخدم فقط للتطوير والاختبار

## أفضل الممارسات

1. **التسمية الواضحة**: استخدم أسماء واضحة للملفات
2. **التاريخ**: أضف التاريخ لأسماء الملفات المهمة
3. **التوثيق**: اكتب تعليقات توضح الغرض من الملف
4. **التنظيم**: استخدم مجلدات فرعية للتنظيم
5. **التنظيف**: احذف الملفات غير المحتاجة بانتظام

## مثال على التسمية الجيدة

```
tmp/
├── prototypes/
│   ├── 2024-01-15_new-dashboard-layout.tsx
│   └── 2024-01-16_improved-search-component.tsx
├── experiments/
│   ├── 2024-01-14_websocket-connection-test.ts
│   └── 2024-01-15_new-auth-flow-experiment.ts
└── data/
    ├── 2024-01-15_sample-bus-routes.json
    └── 2024-01-16_test-user-data.json
```

---

**ملاحظة**: هذا المجلد للتطوير فقط ولا يجب الاعتماد عليه لحفظ ملفات مهمة.
