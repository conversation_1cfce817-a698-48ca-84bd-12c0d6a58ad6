# مجلد الوحدات (Apps/Modules)

هذا المجلد يحتوي على جميع وحدات النظام المعيارية (Modular Plugins) التي يمكن تفعيلها أو تعطيلها لكل مدرسة (Tenant) حسب الحاجة.

## هيكل الوحدات

كل وحدة تتبع نمط Clean Architecture مع الطبقات التالية:

```
apps/<module>/
├── domain/           # نماذج البيانات والواجهات
│   ├── entities/     # الكيانات الأساسية
│   ├── interfaces/   # واجهات التفاعل
│   └── types/        # أنواع البيانات
├── application/      # منطق العمل والخدمات
│   ├── services/     # خدمات العمل
│   ├── usecases/     # حالات الاستخدام
│   └── validators/   # التحقق من البيانات
├── infrastructure/   # التفاعل مع قاعدة البيانات والخدمات الخارجية
│   ├── repositories/ # مستودعات البيانات
│   ├── external/     # الخدمات الخارجية
│   └── database/     # استعلامات قاعدة البيانات
├── ui/               # واجهات المستخدم
│   ├── components/   # مكونات الواجهة
│   ├── pages/        # صفحات الوحدة
│   ├── hooks/        # React Hooks
│   └── styles/       # أنماط CSS
└── tests/            # اختبارات الوحدة
    ├── unit/         # اختبارات الوحدة
    ├── integration/  # اختبارات التكامل
    └── e2e/          # اختبارات شاملة
```

## الوحدات المتاحة

### الوحدات الأساسية (Core Modules)
- **auth** - نظام المصادقة والأمان
- **users** - إدارة المستخدمين
- **tenants** - إدارة المدارس (المستأجرين)
- **rbac** - نظام الأدوار والصلاحيات

### وحدات إدارة الكيانات
- **students** - إدارة الطلاب
- **parents** - إدارة أولياء الأمور
- **drivers** - إدارة السائقين
- **buses** - إدارة الحافلات
- **routes** - إدارة المسارات والنقاط

### الوحدات التشغيلية
- **attendance** - نظام الحضور والانصراف
- **gps-tracking** - التتبع المباشر بـ GPS
- **notifications** - نظام الإشعارات
- **maintenance** - إدارة صيانة الأسطول
- **payments** - نظام الدفع والاشتراكات

### وحدات التقارير والتحليل
- **reports** - التقارير والإحصائيات
- **analytics** - التحليلات المتقدمة
- **dashboards** - لوحات التحكم

### الوحدات المتقدمة
- **emergency** - نظام الطوارئ
- **gamification** - نظام النقاط والتقييم
- **ai-optimization** - تحسين المسارات بالذكاء الاصطناعي
- **biometric** - التحقق البيومتري

## آلية تفعيل الوحدات

يتم تفعيل/تعطيل الوحدات لكل مدرسة من خلال:

1. **قاعدة البيانات**: حقل `enabled_modules` في جدول `tenants`
2. **Backend Middleware**: فحص الصلاحيات قبل تنفيذ العمليات
3. **Frontend Router**: إخفاء/إظهار الروابط حسب الوحدات المفعلة

## إضافة وحدة جديدة

لإضافة وحدة جديدة:

1. إنشاء مجلد باسم الوحدة
2. تطبيق هيكل Clean Architecture
3. إضافة الوحدة إلى قائمة الوحدات المتاحة
4. تحديث Router والقوائم
5. كتابة الاختبارات المطلوبة
6. توثيق الوحدة

## الاعتماديات بين الوحدات

- **auth**: وحدة أساسية مطلوبة لجميع الوحدات
- **users**: مطلوبة لمعظم الوحدات
- **tenants**: وحدة أساسية للنظام متعدد المستأجرين
- **rbac**: مطلوبة للتحكم في الصلاحيات

## أفضل الممارسات

1. **فصل الاهتمامات**: كل طبقة لها مسؤولية محددة
2. **قابلية الاختبار**: كتابة اختبارات شاملة
3. **إعادة الاستخدام**: مشاركة المكونات المشتركة
4. **الأداء**: تحسين الاستعلامات والتحميل
5. **الأمان**: تطبيق مبادئ الأمان في كل طبقة
