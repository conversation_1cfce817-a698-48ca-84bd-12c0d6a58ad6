# إعدادات Supabase - نظام إدارة الحافلات المدرسية
## Supabase Configuration - School Bus Management System

## معلومات المشروع

### تفاصيل المشروع
- **اسم المشروع:** SchoolBus
- **معرف المشروع:** `lfvnkfzlztjnwyluzoii`
- **المنطقة:** `eu-central-1` (أوروبا الوسطى)
- **الحالة:** `ACTIVE_HEALTHY` ✅

### معلومات قاعدة البيانات
- **المضيف:** `db.lfvnkfzlztjnwyluzoii.supabase.co`
- **إصدار PostgreSQL:** `15.8.1.094`
- **محرك PostgreSQL:** `15`
- **قناة الإصدار:** `ga` (General Availability)

## الاتصال بقاعدة البيانات

### متغيرات البيئة المطلوبة
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://lfvnkfzlztjnwyluzoii.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Database Connection (للاتصال المباشر إذا لزم الأمر)
DATABASE_URL=postgresql://postgres:[password]@db.lfvnkfzlztjnwyluzoii.supabase.co:5432/postgres
```

### إعداد Supabase Client
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'school_bus_system'
  },
  global: {
    headers: {
      'x-application-name': 'school-bus-management'
    }
  }
});

// إعداد السياق للمستأجر
export const setTenantContext = async (tenantId: string) => {
  const { error } = await supabase.rpc('set_tenant_context', {
    tenant_uuid: tenantId
  });
  
  if (error) {
    console.error('Error setting tenant context:', error);
    throw error;
  }
};
```

## الامتدادات المفعلة

### امتدادات PostgreSQL
1. **`uuid-ossp`** - لإنشاء معرفات UUID فريدة
2. **`postgis`** - لدعم البيانات الجغرافية والمكانية
3. **`pg_stat_statements`** - لمراقبة أداء الاستعلامات

### التحقق من الامتدادات
```sql
-- فحص الامتدادات المثبتة
SELECT extname, extversion 
FROM pg_extension 
WHERE extname IN ('uuid-ossp', 'postgis', 'pg_stat_statements');
```

## هيكل قاعدة البيانات

### Schema الرئيسي
- **اسم Schema:** `school_bus_system`
- **الجداول:** 19 جدول
- **الفهارس:** 73 فهرس
- **سياسات RLS:** 11 سياسة
- **الدوال:** 6 دوال مساعدة
- **Views:** 5 views للتقارير

### الجداول الأساسية
```sql
-- قائمة الجداول الرئيسية
school_bus_system.tenants           -- المدارس/المستأجرين
school_bus_system.roles             -- الأدوار
school_bus_system.permissions       -- الصلاحيات
school_bus_system.role_permission_map -- ربط الأدوار بالصلاحيات
school_bus_system.users             -- المستخدمين
school_bus_system.students          -- الطلاب
school_bus_system.drivers           -- السائقين
school_bus_system.buses             -- الحافلات
school_bus_system.routes            -- المسارات
school_bus_system.stops             -- نقاط التوقف
school_bus_system.attendance        -- الحضور والانصراف
school_bus_system.notifications     -- الإشعارات
school_bus_system.auth_sessions     -- جلسات المصادقة
school_bus_system.login_logs        -- سجل تسجيل الدخول
```

## Row Level Security (RLS)

### سياسات الأمان المطبقة
```sql
-- فحص سياسات RLS النشطة
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'school_bus_system'
ORDER BY tablename, policyname;
```

### آلية العزل
1. **عزل المستأجرين:** كل جدول يحتوي على `tenant_id`
2. **سياق المستأجر:** يتم تعيينه عند تسجيل الدخول
3. **فحص تلقائي:** جميع الاستعلامات تفحص `tenant_id` تلقائياً
4. **حماية شاملة:** لا يمكن الوصول لبيانات مستأجرين آخرين

## البيانات التجريبية

### مدرسة تجريبية
```sql
-- مدرسة المستقبل النموذجية
SELECT * FROM school_bus_system.tenants 
WHERE name = 'مدرسة المستقبل النموذجية';
```

### مستخدم تجريبي
```sql
-- مدير المدرسة التجريبي
SELECT u.*, r.name_ar as role_name 
FROM school_bus_system.users u
JOIN school_bus_system.roles r ON u.role_id = r.id
WHERE u.email = '<EMAIL>';
```

### بيانات النقل
```sql
-- الحافلة والمسار التجريبي
SELECT 
    b.bus_number,
    b.plate_number,
    r.name as route_name,
    d.name_ar as driver_name
FROM school_bus_system.buses b
JOIN school_bus_system.routes r ON b.id = r.bus_id
JOIN school_bus_system.drivers d ON b.driver_id = d.id;
```

## الدوال المساعدة

### دوال إدارة المستأجرين
```sql
-- تعيين سياق المستأجر
SELECT school_bus_system.set_tenant_context('550e8400-e29b-41d4-a716-446655440001');

-- الحصول على المستأجر الحالي
SELECT school_bus_system.get_current_tenant_id();
```

### دوال جغرافية
```sql
-- حساب المسافة بين نقطتين (بالكيلومتر)
SELECT school_bus_system.calculate_distance(
    ST_GeogFromText('POINT(46.7219 24.6877)'),
    ST_GeogFromText('POINT(46.7319 24.6977)')
);

-- فحص النقاط داخل نطاق معين
SELECT school_bus_system.is_within_radius(
    ST_GeogFromText('POINT(46.7219 24.6877)'),
    ST_GeogFromText('POINT(46.7319 24.6977)'),
    1000 -- 1000 متر
);
```

### فحص صحة النظام
```sql
-- فحص شامل لصحة قاعدة البيانات
SELECT * FROM school_bus_system.database_health_check();
```

## Views للتقارير

### إحصائيات الحضور
```sql
-- ملخص حضور الطلاب
SELECT * FROM school_bus_system.student_attendance_summary;

-- إحصائيات يومية
SELECT * FROM school_bus_system.daily_attendance_stats;
```

### إحصائيات الحافلات
```sql
-- استخدام الحافلات
SELECT * FROM school_bus_system.bus_utilization_summary;

-- أداء المسارات
SELECT * FROM school_bus_system.route_performance_summary;
```

### نظرة عامة على المستأجرين
```sql
-- إحصائيات شاملة للمدارس
SELECT * FROM school_bus_system.tenant_overview;
```

## الأمان والصلاحيات

### أفضل الممارسات
1. **استخدام ANON KEY:** للعمليات العامة فقط
2. **SERVICE ROLE KEY:** للعمليات الإدارية فقط
3. **تعيين السياق:** دائماً قبل العمليات
4. **فحص الصلاحيات:** في كل طلب
5. **تشفير البيانات:** للمعلومات الحساسة

### مثال على الاستخدام الآمن
```typescript
// تسجيل دخول آمن مع تعيين السياق
export const secureLogin = async (email: string, password: string) => {
  // 1. تسجيل الدخول
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  if (authError) throw authError;
  
  // 2. جلب بيانات المستخدم
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*, tenant_id, role:roles(*)')
    .eq('id', authData.user.id)
    .single();
  
  if (userError) throw userError;
  
  // 3. تعيين سياق المستأجر
  await setTenantContext(userData.tenant_id);
  
  return { user: userData, session: authData.session };
};
```

## المراقبة والصيانة

### مراقبة الأداء
```sql
-- أبطأ الاستعلامات
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;
```

### مراقبة المساحة
```sql
-- حجم الجداول
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'school_bus_system'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### النسخ الاحتياطية
- **تلقائية:** Supabase يقوم بنسخ احتياطية تلقائية
- **يدوية:** يمكن إنشاء نسخ احتياطية حسب الحاجة
- **الاستعادة:** متاحة عبر لوحة تحكم Supabase

## الخطوات التالية

### للمطورين
1. **إعداد البيئة المحلية:** نسخ متغيرات البيئة
2. **تثبيت Supabase CLI:** للتطوير المحلي
3. **إعداد TypeScript Types:** لتحسين تجربة التطوير
4. **اختبار الاتصال:** التأكد من عمل جميع الوظائف

### للنشر
1. **إعداد متغيرات الإنتاج:** في بيئة النشر
2. **تفعيل SSL:** للاتصالات الآمنة
3. **مراقبة الأداء:** إعداد تنبيهات
4. **النسخ الاحتياطية:** جدولة نسخ احتياطية منتظمة

---

✅ **قاعدة البيانات جاهزة بالكامل ومُحسنة للأداء والأمان!**
