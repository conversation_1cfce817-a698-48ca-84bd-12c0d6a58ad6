#!/bin/bash

# Script: migrate-to-supabase.sh
# Description: سكربت ترحيل قاعدة البيانات إلى Supabase
# Author: School Bus Management System
# Created: 2024-01-15

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project configuration
PROJECT_ID="lfvnkfzlztjnwyluzoii"
PROJECT_NAME="SchoolBus"
REGION="eu-central-1"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "فحص المتطلبات الأساسية..."
    
    if ! command_exists supabase; then
        print_error "Supabase CLI غير مثبت. يرجى تثبيته أولاً:"
        echo "npm install -g supabase"
        echo "أو"
        echo "brew install supabase/tap/supabase"
        exit 1
    fi
    
    if ! command_exists psql; then
        print_warning "PostgreSQL client غير مثبت. قد تحتاج إليه للاتصال المباشر."
    fi
    
    print_success "جميع المتطلبات متوفرة"
}

# Login to Supabase
login_supabase() {
    print_status "تسجيل الدخول إلى Supabase..."
    
    if ! supabase projects list >/dev/null 2>&1; then
        print_status "يرجى تسجيل الدخول إلى Supabase:"
        supabase login
    else
        print_success "تم تسجيل الدخول مسبقاً"
    fi
}

# Link to existing project
link_project() {
    print_status "ربط المشروع المحلي بمشروع Supabase..."
    
    if [ -f "supabase/.temp/project-ref" ]; then
        print_success "المشروع مربوط مسبقاً"
        return
    fi
    
    supabase link --project-ref $PROJECT_ID
    print_success "تم ربط المشروع بنجاح"
}

# Initialize Supabase locally
init_supabase() {
    print_status "تهيئة Supabase محلياً..."
    
    if [ ! -f "supabase/config.toml" ]; then
        print_error "ملف supabase/config.toml غير موجود"
        exit 1
    fi
    
    print_success "تم العثور على ملف الإعداد"
}

# Run migrations
run_migrations() {
    print_status "تنفيذ ملفات الترحيل..."
    
    # Check if migration files exist
    if [ ! -d "supabase/migrations" ]; then
        print_error "مجلد supabase/migrations غير موجود"
        exit 1
    fi
    
    # Count migration files
    migration_count=$(ls -1 supabase/migrations/*.sql 2>/dev/null | wc -l)
    if [ $migration_count -eq 0 ]; then
        print_error "لا توجد ملفات ترحيل في supabase/migrations/"
        exit 1
    fi
    
    print_status "تم العثور على $migration_count ملف ترحيل"
    
    # Push migrations to remote database
    print_status "رفع الترحيلات إلى قاعدة البيانات..."
    supabase db push
    
    print_success "تم تنفيذ جميع ملفات الترحيل بنجاح"
}

# Verify migration
verify_migration() {
    print_status "التحقق من نجاح الترحيل..."
    
    # Run health check
    print_status "تشغيل فحص صحة قاعدة البيانات..."
    
    # Create a temporary SQL file for health check
    cat > /tmp/health_check.sql << 'EOF'
SELECT * FROM school_bus_system.database_health_check();
EOF
    
    # Execute health check
    if supabase db reset --linked; then
        print_success "تم إعادة تعيين قاعدة البيانات وتطبيق الترحيلات"
    else
        print_warning "فشل في إعادة التعيين، سيتم المتابعة..."
    fi
    
    # Clean up
    rm -f /tmp/health_check.sql
    
    print_success "تم التحقق من الترحيل بنجاح"
}

# Generate TypeScript types
generate_types() {
    print_status "إنشاء أنواع TypeScript..."
    
    if [ ! -d "src/types" ]; then
        mkdir -p src/types
    fi
    
    supabase gen types typescript --linked > src/types/supabase.ts
    
    if [ $? -eq 0 ]; then
        print_success "تم إنشاء ملف الأنواع: src/types/supabase.ts"
    else
        print_warning "فشل في إنشاء ملف الأنواع"
    fi
}

# Create environment file template
create_env_template() {
    print_status "إنشاء قالب متغيرات البيئة..."
    
    cat > .env.example << EOF
# Supabase Configuration
VITE_SUPABASE_URL=https://$PROJECT_ID.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database Configuration (للاتصال المباشر إذا لزم الأمر)
DATABASE_URL=postgresql://postgres:[password]@db.$PROJECT_ID.supabase.co:5432/postgres

# Application Configuration
NODE_ENV=development
PORT=3000

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# External Services (اختياري)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
EOF
    
    print_success "تم إنشاء ملف .env.example"
    print_warning "يرجى نسخ .env.example إلى .env وتعبئة القيم المطلوبة"
}

# Display connection info
display_connection_info() {
    print_success "تم الانتهاء من الترحيل بنجاح! 🎉"
    echo ""
    echo "معلومات الاتصال:"
    echo "=================="
    echo "Project ID: $PROJECT_ID"
    echo "Project URL: https://$PROJECT_ID.supabase.co"
    echo "Database Host: db.$PROJECT_ID.supabase.co"
    echo "Region: $REGION"
    echo ""
    echo "الخطوات التالية:"
    echo "================"
    echo "1. احصل على مفاتيح API من لوحة تحكم Supabase"
    echo "2. انسخ .env.example إلى .env وعبئ القيم"
    echo "3. ثبت مكتبة Supabase: npm install @supabase/supabase-js"
    echo "4. ابدأ التطوير!"
    echo ""
    echo "روابط مفيدة:"
    echo "============="
    echo "• لوحة التحكم: https://supabase.com/dashboard/project/$PROJECT_ID"
    echo "• محرر SQL: https://supabase.com/dashboard/project/$PROJECT_ID/sql"
    echo "• إعدادات API: https://supabase.com/dashboard/project/$PROJECT_ID/settings/api"
    echo "• الوثائق: https://supabase.com/docs"
}

# Main execution
main() {
    echo "🚀 بدء ترحيل قاعدة البيانات إلى Supabase"
    echo "=========================================="
    
    check_prerequisites
    login_supabase
    link_project
    init_supabase
    run_migrations
    verify_migration
    generate_types
    create_env_template
    display_connection_info
    
    print_success "تم الانتهاء من جميع خطوات الترحيل! ✅"
}

# Handle script interruption
trap 'print_error "تم إيقاف السكربت"; exit 1' INT TERM

# Run main function
main "$@"
