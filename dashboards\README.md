# لوحات التحكم (Dashboards)

هذا المجلد يحتوي على جميع لوحات التحكم المخصصة لكل دور في النظام.

## أنواع لوحات التحكم

### 1. System Admin Dashboard
**المسار:** `/dashboards/system-admin/`
**الصلاحيات:** System Admin فقط

**المميزات:**
- إحصائيات عامة لجميع المدارس
- إدارة المدارس (Tenants)
- مراقبة أداء النظام
- إدارة المستخدمين العامة
- تقارير شاملة

**المكونات:**
- `SystemAdminDashboard.tsx` - اللوحة الرئيسية
- `TenantsOverview.tsx` - نظرة عامة على المدارس
- `SystemMetrics.tsx` - مقاييس النظام
- `GlobalReports.tsx` - التقارير العامة

### 2. School Admin Dashboard
**المسار:** `/dashboards/school-admin/`
**الصلاحيات:** School Admin

**المميزات:**
- إحصائيات المدرسة
- إدارة المستخدمين داخل المدرسة
- مراقبة الحافلات والمسارات
- تقارير الحضور والأداء
- إدارة الإشعارات

**المكونات:**
- `SchoolAdminDashboard.tsx` - اللوحة الرئيسية
- `SchoolMetrics.tsx` - مقاييس المدرسة
- `BusesOverview.tsx` - نظرة عامة على الحافلات
- `AttendanceStats.tsx` - إحصائيات الحضور

### 3. Driver Dashboard
**المسار:** `/dashboards/driver/`
**الصلاحيات:** Driver

**المميزات:**
- معلومات الرحلة الحالية
- قائمة الطلاب المسجلين
- خريطة المسار
- تسجيل الحضور والانصراف
- حالة الحافلة والصيانة

**المكونات:**
- `DriverDashboard.tsx` - اللوحة الرئيسية
- `CurrentTrip.tsx` - الرحلة الحالية
- `StudentsList.tsx` - قائمة الطلاب
- `RouteMap.tsx` - خريطة المسار
- `BusStatus.tsx` - حالة الحافلة

### 4. Parent Dashboard
**المسار:** `/dashboards/parent/`
**الصلاحيات:** Parent

**المميزات:**
- معلومات الأبناء
- تتبع موقع الحافلة
- سجل الحضور والانصراف
- الإشعارات والرسائل
- المدفوعات والاشتراكات

**المكونات:**
- `ParentDashboard.tsx` - اللوحة الرئيسية
- `ChildrenInfo.tsx` - معلومات الأبناء
- `BusTracking.tsx` - تتبع الحافلة
- `AttendanceHistory.tsx` - سجل الحضور
- `PaymentStatus.tsx` - حالة المدفوعات

### 5. Student Dashboard
**المسار:** `/dashboards/student/`
**الصلاحيات:** Student

**المميزات:**
- المعلومات الشخصية
- جدول الحضور
- معلومات الحافلة والمسار
- النقاط والإنجازات (Gamification)
- الإشعارات الشخصية

**المكونات:**
- `StudentDashboard.tsx` - اللوحة الرئيسية
- `PersonalInfo.tsx` - المعلومات الشخصية
- `AttendanceRecord.tsx` - سجل الحضور
- `BusInfo.tsx` - معلومات الحافلة
- `Achievements.tsx` - الإنجازات والنقاط

### 6. Traffic Supervisor Dashboard
**المسار:** `/dashboards/traffic-supervisor/`
**الصلاحيات:** Traffic Supervisor

**المميزات:**
- مراقبة جميع الحافلات
- إنذارات الطوارئ
- تتبع المسارات
- تقارير الحوادث
- التواصل مع السائقين

**المكونات:**
- `TrafficSupervisorDashboard.tsx` - اللوحة الرئيسية
- `AllBusesMap.tsx` - خريطة جميع الحافلات
- `EmergencyAlerts.tsx` - إنذارات الطوارئ
- `RouteMonitoring.tsx` - مراقبة المسارات
- `IncidentReports.tsx` - تقارير الحوادث

## المكونات المشتركة

### Widgets
- `MetricCard.tsx` - بطاقة المقاييس
- `ChartWidget.tsx` - ودجت الرسوم البيانية
- `NotificationWidget.tsx` - ودجت الإشعارات
- `QuickActions.tsx` - الإجراءات السريعة

### Charts
- `AttendanceChart.tsx` - رسم بياني للحضور
- `BusUtilizationChart.tsx` - رسم بياني لاستخدام الحافلات
- `PerformanceChart.tsx` - رسم بياني للأداء
- `TrendChart.tsx` - رسم بياني للاتجاهات

### Maps
- `LiveTrackingMap.tsx` - خريطة التتبع المباشر
- `RouteMap.tsx` - خريطة المسارات
- `OverviewMap.tsx` - خريطة عامة

## التخصيص والثيمات

### نظام الألوان
- **Primary:** #3b82f6 (أزرق)
- **Secondary:** #14b8a6 (تركوازي)
- **Accent:** #10b981 (أخضر)
- **Warning:** #f59e0b (برتقالي)
- **Error:** #ef4444 (أحمر)

### الخطوط
- **العربية:** Cairo
- **الإنجليزية:** Inter

### الوضع الداكن
جميع لوحات التحكم تدعم الوضع الداكن مع تبديل تلقائي للألوان.

## التحديث المباشر (Real-time)

جميع لوحات التحكم تستخدم Supabase Realtime للتحديث المباشر:
- تحديث مواقع الحافلات
- إشعارات فورية
- تحديث إحصائيات الحضور
- تنبيهات الطوارئ

## الاستجابة (Responsive Design)

جميع لوحات التحكم مصممة للعمل على:
- أجهزة سطح المكتب (Desktop)
- الأجهزة اللوحية (Tablet)
- الهواتف الذكية (Mobile)

## الأمان والصلاحيات

- فحص الصلاحيات على مستوى المكون
- عرض البيانات حسب الدور
- حماية المسارات الحساسة
- تشفير البيانات الحساسة
