# إعداد البنية الأساسية - الوثائق التفصيلية
## Initial Setup - Detailed Documentation

## نظرة عامة

المرحلة الأولى تركز على إنشاء أساس قوي ومرن لنظام إدارة الحافلات المدرسية. تم تصميم البنية لتدعم آلاف المدارس مع ضمان الأمان والأداء العالي.

## المعمارية (Architecture)

### الهيكل العام
```
schoolBus/
├── apps/                    # الوحدات المعيارية
├── dashboards/              # لوحات التحكم
├── ui/                      # مكتبة واجهة المستخدم
├── scripts/                 # سكربتات التطوير
├── test/                    # الاختبارات
├── tmp/                     # ملفات مؤقتة
├── docs/                    # الوثائق
├── README.md                # وصف المشروع
└── .env.example             # قالب متغيرات البيئة
```

### المبادئ المعمارية المطبقة

#### 1. Clean Architecture
```mermaid
graph TB
    subgraph "Presentation Layer"
        A[UI Components]
        B[Pages]
        C[Layouts]
    end
    
    subgraph "Application Layer"
        D[Use Cases]
        E[Services]
        F[Hooks]
    end
    
    subgraph "Domain Layer"
        G[Entities]
        H[Business Logic]
        I[Interfaces]
    end
    
    subgraph "Infrastructure Layer"
        J[Database]
        K[External APIs]
        L[File Storage]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
    G --> J
    H --> K
    I --> L
```

#### 2. Modular Architecture
كل وحدة في مجلد `apps/` تتبع نفس الهيكل:
```
apps/[module]/
├── domain/           # منطق العمل الأساسي
├── application/      # حالات الاستخدام
├── infrastructure/   # التفاعل مع الخدمات الخارجية
├── ui/               # واجهات المستخدم
└── tests/            # اختبارات الوحدة
```

#### 3. Multi-Tenant Architecture
- **عزل البيانات**: Row Level Security (RLS)
- **عزل الموارد**: tenant_id في كل جدول
- **عزل الإعدادات**: إعدادات مخصصة لكل مدرسة

## قاعدة البيانات (Database)

### التصميم المفاهيمي
```mermaid
erDiagram
    TENANTS ||--o{ USERS : contains
    USERS ||--o{ AUTH_SESSIONS : has
    USERS ||--o{ LOGIN_LOGS : generates
    ROLES ||--o{ USERS : assigned_to
    ROLES ||--o{ ROLE_PERMISSION_MAP : has
    PERMISSIONS ||--o{ ROLE_PERMISSION_MAP : belongs_to
    
    TENANTS {
        uuid id PK
        string name
        string domain
        string subdomain
        jsonb settings
        boolean is_active
    }
    
    USERS {
        uuid id PK
        string email UK
        string name_ar
        uuid tenant_id FK
        uuid role_id FK
        boolean is_active
    }
    
    ROLES {
        uuid id PK
        string name UK
        string name_ar
        string role_scope
        boolean is_system_role
    }
```

### الجداول الأساسية

#### 1. tenants (المدارس/المستأجرين)
```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(100) UNIQUE,
    subdomain VARCHAR(50) UNIQUE,
    
    -- ألوان مخصصة للمدرسة
    primary_color VARCHAR(7) DEFAULT '#3b82f6',
    secondary_color VARCHAR(7) DEFAULT '#14b8a6',
    accent_color VARCHAR(7) DEFAULT '#10b981',
    warning_color VARCHAR(7) DEFAULT '#f59e0b',
    error_color VARCHAR(7) DEFAULT '#ef4444',
    
    -- الوحدات المفعلة
    enabled_modules TEXT[] DEFAULT ARRAY['auth', 'users', 'students', 'buses'],
    
    -- إعدادات إضافية
    settings JSONB DEFAULT '{}',
    
    -- حالة النشاط
    is_active BOOLEAN DEFAULT true,
    
    -- طوابع زمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**الميزات:**
- **ألوان مخصصة**: كل مدرسة يمكنها تخصيص ألوانها
- **وحدات قابلة للتفعيل**: تحكم في الميزات المتاحة
- **إعدادات مرنة**: JSONB للإعدادات المخصصة
- **فهارس محسنة**: للبحث السريع

#### 2. users (المستخدمين)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    
    -- الأسماء (دعم متعدد اللغات)
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    
    -- معلومات الاتصال
    phone VARCHAR(20),
    profile_photo_url TEXT,
    
    -- الانتماء والأدوار
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id),
    
    -- حالة الحساب
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    
    -- المصادقة الثنائية
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret VARCHAR(32),
    
    -- آخر تسجيل دخول
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    -- طوابع زمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**الميزات:**
- **دعم متعدد اللغات**: أسماء بالعربية والإنجليزية
- **مصادقة ثنائية**: دعم 2FA مدمج
- **تتبع النشاط**: آخر تسجيل دخول
- **مرونة في البيانات**: حقول اختيارية

#### 3. roles (الأدوار)
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- نطاق الدور
    role_scope VARCHAR(20) DEFAULT 'tenant' 
        CHECK (role_scope IN ('global', 'tenant', 'assigned', 'children', 'personal')),
    
    -- هل هو دور نظام أساسي
    is_system_role BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**أنواع النطاقات:**
- **global**: صلاحيات على مستوى النظام (System Admin)
- **tenant**: صلاحيات على مستوى المدرسة (School Admin)
- **assigned**: صلاحيات على الموارد المعينة (Driver)
- **children**: صلاحيات على الأطفال المرتبطين (Parent)
- **personal**: صلاحيات شخصية فقط (Student)

### Row Level Security (RLS)

#### تطبيق العزل الآمن
```sql
-- تفعيل RLS على جدول المستخدمين
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- سياسة العزل بين المستأجرين
CREATE POLICY "users_tenant_isolation" ON users
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- دالة تعيين السياق
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### كيفية العمل
1. **تعيين السياق**: عند تسجيل الدخول، يتم تعيين `tenant_id`
2. **فحص تلقائي**: كل استعلام يفحص `tenant_id` تلقائياً
3. **عزل كامل**: لا يمكن الوصول لبيانات مستأجرين آخرين

## واجهة المستخدم (Frontend)

### مكتبة UI الشاملة

#### المكونات الأساسية
```typescript
// Button Component
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'warning' | 'error' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

export function Button({ 
  variant = 'primary', 
  size = 'md', 
  loading = false,
  disabled = false,
  children,
  onClick 
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors';
  const variantClasses = {
    primary: 'bg-primary-500 text-white hover:bg-primary-600',
    secondary: 'bg-secondary-500 text-white hover:bg-secondary-600',
    // ... باقي الأنواع
  };
  
  return (
    <button
      className={cn(baseClasses, variantClasses[variant])}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && <LoadingSpinner />}
      {children}
    </button>
  );
}
```

#### نظام الثيمات
```typescript
// Theme Configuration
interface ThemeConfig {
  colors: {
    primary: ColorScale;
    secondary: ColorScale;
    accent: ColorScale;
    warning: ColorScale;
    error: ColorScale;
    gray: ColorScale;
  };
  typography: {
    fontFamily: {
      sans: string[];
      serif: string[];
      mono: string[];
    };
    fontSize: Record<string, string>;
    fontWeight: Record<string, string>;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
}

// استخدام الثيم
const theme = useTheme();
const buttonStyle = {
  backgroundColor: theme.colors.primary[500],
  color: theme.colors.white,
  padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
  borderRadius: theme.borderRadius.md,
};
```

#### دعم RTL/LTR
```css
/* دعم الاتجاه */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* متغيرات CSS للاتجاه */
[dir="rtl"] {
  --margin-start: margin-right;
  --margin-end: margin-left;
  --padding-start: padding-right;
  --padding-end: padding-left;
}

[dir="ltr"] {
  --margin-start: margin-left;
  --margin-end: margin-right;
  --padding-start: padding-left;
  --padding-end: padding-right;
}
```

### تخطيط التطبيق
```typescript
// App Layout Structure
export function AppLayout({ children }: { children: React.ReactNode }) {
  const { user, tenant } = useAuth();
  const { theme } = useTheme();
  const { language, direction } = useLanguage();
  
  return (
    <div className="min-h-screen bg-background" dir={direction}>
      <Header user={user} tenant={tenant} />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
      <Footer />
    </div>
  );
}
```

## الأمان (Security)

### طبقات الحماية المتعددة

#### 1. Network Security
- **Firewall**: CloudFlare WAF
- **DDoS Protection**: CloudFlare
- **Rate Limiting**: على مستوى التطبيق

#### 2. Application Security
- **Authentication**: JWT + 2FA
- **Authorization**: RBAC
- **Input Validation**: Zod schemas
- **CSRF Protection**: مدمج في النماذج

#### 3. Database Security
- **Row Level Security**: عزل البيانات
- **Encryption at Rest**: AES-256
- **Encrypted Backups**: نسخ احتياطية مشفرة
- **Audit Logging**: تسجيل جميع العمليات

#### 4. Infrastructure Security
- **Secrets Management**: متغيرات البيئة
- **Monitoring**: مراقبة الأنشطة المشبوهة
- **Updates**: تحديثات أمنية تلقائية

### تطبيق الأمان في الكود
```typescript
// Middleware للتحقق من الصلاحيات
export const requirePermission = (permission: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 1. التحقق من JWT
      const token = extractToken(req);
      const payload = await verifyJWT(token);
      
      // 2. جلب بيانات المستخدم
      const user = await getUserById(payload.userId);
      if (!user.isActive) {
        throw new UnauthorizedError('User account is disabled');
      }
      
      // 3. تعيين سياق المستأجر
      await setTenantContext(user.tenantId);
      
      // 4. فحص الصلاحية
      const hasPermission = await checkUserPermission(user.id, permission);
      if (!hasPermission) {
        throw new ForbiddenError('Insufficient permissions');
      }
      
      // 5. إضافة بيانات المستخدم للطلب
      req.user = user;
      req.tenant = await getTenantById(user.tenantId);
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

// استخدام Middleware
app.get('/api/students', 
  requirePermission('STUDENTS_VIEW'),
  getStudents
);
```

## الأداء (Performance)

### استراتيجيات التحسين

#### 1. Database Optimization
```sql
-- فهارس محسنة للاستعلامات الشائعة
CREATE INDEX CONCURRENTLY idx_users_tenant_active 
ON users(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_users_email_lower 
ON users(LOWER(email));

-- Partial indexes للبيانات النشطة فقط
CREATE INDEX CONCURRENTLY idx_active_tenants 
ON tenants(id) WHERE is_active = true;
```

#### 2. Frontend Optimization
```typescript
// Code Splitting
const StudentManagement = lazy(() => import('./apps/students/ui/pages/StudentManagement'));
const BusTracking = lazy(() => import('./apps/gps-tracking/ui/pages/BusTracking'));

// Memoization
const ExpensiveComponent = memo(({ data }: { data: ComplexData }) => {
  const processedData = useMemo(() => {
    return processComplexData(data);
  }, [data]);
  
  return <div>{processedData}</div>;
});

// Virtual Scrolling للقوائم الطويلة
import { FixedSizeList as List } from 'react-window';

const StudentList = ({ students }: { students: Student[] }) => (
  <List
    height={600}
    itemCount={students.length}
    itemSize={60}
    itemData={students}
  >
    {StudentRow}
  </List>
);
```

#### 3. Caching Strategy
```typescript
// Multi-level caching
const cacheStrategy = {
  // Browser cache
  browser: {
    staticAssets: '1y',
    apiResponses: '5m',
  },
  
  // Application cache
  application: {
    userSessions: '24h',
    tenantConfig: '1h',
    staticData: '30m',
  },
  
  // Database cache
  database: {
    queryResults: '15m',
    aggregations: '5m',
  },
};
```

## الاختبارات (Testing)

### استراتيجية الاختبار الشاملة

#### 1. Unit Tests
```typescript
// اختبار مكون Button
describe('Button Component', () => {
  it('renders with correct variant styles', () => {
    render(<Button variant="primary">Test</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-primary-500');
  });
  
  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('shows loading state', () => {
    render(<Button loading>Loading</Button>);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
```

#### 2. Integration Tests
```typescript
// اختبار RLS
describe('Row Level Security', () => {
  it('prevents cross-tenant data access', async () => {
    // إنشاء مستأجرين منفصلين
    const tenant1 = await createTestTenant('School A');
    const tenant2 = await createTestTenant('School B');
    
    // إنشاء مستخدمين
    const user1 = await createTestUser(tenant1.id);
    const user2 = await createTestUser(tenant2.id);
    
    // تسجيل دخول المستخدم الأول
    await setTenantContext(tenant1.id);
    
    // محاولة الوصول لبيانات المستأجر الثاني
    const result = await supabase
      .from('users')
      .select('*')
      .eq('tenant_id', tenant2.id);
    
    expect(result.data).toHaveLength(0);
  });
});
```

#### 3. E2E Tests
```typescript
// اختبار تدفق كامل
describe('User Management Flow', () => {
  it('creates and manages users', () => {
    // تسجيل دخول المدير
    cy.login('<EMAIL>', 'password');
    
    // الانتقال لصفحة المستخدمين
    cy.visit('/users');
    
    // إضافة مستخدم جديد
    cy.get('[data-testid="add-user-button"]').click();
    cy.get('[data-testid="name-input"]').type('أحمد محمد');
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="role-select"]').select('driver');
    cy.get('[data-testid="save-button"]').click();
    
    // التحقق من إضافة المستخدم
    cy.contains('أحمد محمد').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
  });
});
```

## النشر والصيانة (Deployment & Maintenance)

### استراتيجية النشر
```bash
#!/bin/bash
# سكربت النشر الآلي

echo "🚀 بدء عملية النشر..."

# 1. فحص الكود
npm run lint
npm run type-check
npm run test

# 2. بناء المشروع
npm run build

# 3. نشر قاعدة البيانات
supabase db push

# 4. نشر Functions
supabase functions deploy

# 5. نشر الواجهة الأمامية
npm run deploy:production

echo "✅ تم النشر بنجاح"
```

### المراقبة والصيانة
```typescript
// Health Check Endpoint
app.get('/health', async (req, res) => {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkExternalServices(),
    checkFileStorage(),
  ]);
  
  const health = {
    status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks: checks.map((check, index) => ({
      name: ['database', 'external', 'storage'][index],
      status: check.status,
      details: check.status === 'fulfilled' ? check.value : check.reason,
    })),
  };
  
  res.status(health.status === 'healthy' ? 200 : 503).json(health);
});
```

## الخلاصة

المرحلة الأولى أسست بنية قوية ومرنة تدعم:
- **الأمان المتقدم** مع RLS وطبقات حماية متعددة
- **الأداء العالي** مع فهارس محسنة واستراتيجيات تخزين مؤقت
- **قابلية التوسع** لدعم آلاف المدارس
- **سهولة الصيانة** مع كود منظم ووثائق شاملة
- **جودة عالية** مع اختبارات شاملة

هذا الأساس جاهز لبناء جميع ميزات النظام عليه في المراحل القادمة.
